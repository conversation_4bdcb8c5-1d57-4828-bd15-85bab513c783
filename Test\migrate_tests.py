"""
Test Migration Script

This script helps migrate existing test files to the new modular framework.
It analyzes existing tests and generates new test files using the base framework.
"""

import os
import re
import ast
import glob
from typing import Dict, List, Tuple, Any
from Test.test_config import TEST_DATA_CONFIGS


class TestMigrator:
    """
    Migrates existing test files to the new framework structure.
    """
    
    def __init__(self):
        self.test_dir = "Test"
        self.existing_tests = []
        self.migration_report = []
    
    def discover_existing_tests(self) -> List[str]:
        """
        Discover existing test files that need migration.
        
        Returns:
            List of test file paths
        """
        pattern = os.path.join(self.test_dir, "test_*.py")
        test_files = glob.glob(pattern)
        
        # Filter out new framework files
        exclude_files = [
            'test_config.py', 'base_test.py', 'test_runner.py', 
            'config_manager.py', 'migrate_tests.py'
        ]
        
        existing_tests = []
        for test_file in test_files:
            filename = os.path.basename(test_file)
            if filename not in exclude_files and not filename.endswith('_new.py'):
                existing_tests.append(test_file)
        
        self.existing_tests = existing_tests
        return existing_tests
    
    def analyze_test_file(self, test_file_path: str) -> Dict[str, Any]:
        """
        Analyze an existing test file to extract configuration and structure.
        
        Args:
            test_file_path: Path to the test file
            
        Returns:
            Dict containing analysis results
        """
        with open(test_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        analysis = {
            'file_path': test_file_path,
            'module_name': os.path.splitext(os.path.basename(test_file_path))[0],
            'class_names': [],
            'test_methods': [],
            'configurations': {},
            'imports': [],
            'global_vars': {}
        }
        
        try:
            tree = ast.parse(content)
            
            # Extract imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        analysis['imports'].append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ''
                    for alias in node.names:
                        analysis['imports'].append(f"{module}.{alias.name}")
            
            # Extract global variables
            for node in tree.body:
                if isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            try:
                                value = ast.literal_eval(node.value)
                                analysis['global_vars'][target.id] = value
                            except:
                                # Handle non-literal values
                                if isinstance(node.value, ast.Str):
                                    analysis['global_vars'][target.id] = node.value.s
                                elif isinstance(node.value, ast.Constant):
                                    analysis['global_vars'][target.id] = node.value.value
            
            # Extract classes and methods
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    analysis['class_names'].append(node.name)
                    
                    # Extract test methods from class
                    for item in node.body:
                        if isinstance(item, ast.AsyncFunctionDef) and item.name.startswith('test_'):
                            analysis['test_methods'].append(item.name)
        
        except Exception as e:
            print(f"Error analyzing {test_file_path}: {str(e)}")
        
        # Extract configuration from content using regex
        self._extract_config_from_content(content, analysis)
        
        return analysis
    
    def _extract_config_from_content(self, content: str, analysis: Dict[str, Any]):
        """
        Extract configuration information from file content using regex.
        
        Args:
            content: File content
            analysis: Analysis dictionary to update
        """
        # Extract doc_name pattern
        doc_name_match = re.search(r'doc_name\s*=\s*["\']([^"\']+)["\']', content)
        if doc_name_match:
            analysis['configurations']['doc_name'] = doc_name_match.group(1)
        
        # Extract pdf_filename pattern
        pdf_filename_match = re.search(r'pdf_filename\s*=\s*["\']([^"\']+)["\']', content)
        if pdf_filename_match:
            analysis['configurations']['pdf_filename'] = pdf_filename_match.group(1)
        
        # Extract XML pattern
        xml_pattern_match = re.search(r'extract_zip\([^,]+,\s*[^,]+,\s*["\']([^"\']+)["\']', content)
        if xml_pattern_match:
            analysis['configurations']['xml_extract_pattern'] = xml_pattern_match.group(1)
        
        # Extract CSV pattern
        csv_pattern_match = re.search(r'extract_zip\([^,]+,\s*[^,]+,\s*[^,]+,\s*["\']([^"\']+)["\']', content)
        if csv_pattern_match:
            analysis['configurations']['csv_extract_pattern'] = csv_pattern_match.group(1)
    
    def generate_new_test_file(self, analysis: Dict[str, Any]) -> str:
        """
        Generate a new test file using the framework.
        
        Args:
            analysis: Analysis results from existing test
            
        Returns:
            Generated test file content
        """
        module_name = analysis['module_name']
        class_name = analysis['class_names'][0] if analysis['class_names'] else f"Test{module_name.replace('test_', '').title()}"
        
        # Generate class name from module name
        if not class_name.startswith('Test'):
            class_name = f"Test{class_name}"
        
        template = f'''"""
Test Module: {module_name.replace('test_', '').replace('_', ' ').title()}

This module contains tests migrated to the new modular test framework.
Auto-generated from: {analysis['file_path']}
"""

import os
from Test.base_test import BaseIndianInvTest
from Test.test_config import TestConfig, get_test_data_config


class {class_name}(BaseIndianInvTest):
    """
    Test class migrated to new framework.
    
    This class inherits from BaseIndianInvTest which provides all common functionality
    including automatic directory setup, logging, and file comparison utilities.
    """
'''
        
        # Generate test methods
        for test_method in analysis['test_methods']:
            method_template = f'''
    async def {test_method}(self):
        """
        Migrated test method: {test_method}
        
        This test:
        1. Sends a PDF file to the API
        2. Receives a ZIP response
        3. Extracts XML and CSV files
        4. Renames files to standard names
        5. Compares with reference files (if not in BRELEARN mode)
        6. Stores results in unique directory to avoid overwriting
        """
        self.logger.MSWriteLog("info", f"Executing test: {{self._testMethodName}}")
        
        try:
            # Get test-specific configuration
            if not self.test_data_config:
                self.logger.MSWriteLog("error", "No test data configuration found")
                self.fail("No test data configuration found")
            
            doc_name = self.test_data_config['doc_name']
            pdf_filename = self.test_data_config['pdf_filename']
            xml_extract_pattern = self.test_data_config['xml_extract_pattern']
            csv_extract_pattern = self.test_data_config['csv_extract_pattern']
            
            # Verify license
            license_data = self.verify_license()
            
            # Get file paths (automatically uses module name for directories)
            file_paths = self.get_file_paths(doc_name, pdf_filename)
            
            # Verify input files exist
            self.verify_input_files(file_paths)
            
            # Check server availability
            if not self.is_server_listening():
                self.logger.MSWriteLog("error", f"Server at {{TestConfig.API_URL}} is not available")
                self.fail(f"Server at {{TestConfig.API_URL}} is not available")
            
            # Create unique result directory for this test run
            unique_result_dir = self.get_unique_result_directory(pdf_filename)
            
            # Update file paths to use unique result directory if in BRELEARN mode
            if TestConfig.BRELEARN:
                file_paths['output_dir'] = unique_result_dir
                file_paths['zip_file'] = os.path.join(unique_result_dir, "Content_zipFile.zip")
                file_paths['output_xml'] = os.path.join(unique_result_dir, f"{{doc_name}}.xml")
                file_paths['output_csv'] = os.path.join(unique_result_dir, f"Report_{{doc_name}}.csv")
            
            # Send API request
            response = await self.send_api_request(file_paths, pdf_filename, license_data)
            
            # Validate and save response
            self.validate_and_save_response(response, file_paths['zip_file'])
            
            # Extract and rename files
            xml_path, csv_path = self.extract_and_rename_files(
                file_paths['zip_file'],
                file_paths['output_dir'],
                doc_name,
                xml_extract_pattern,
                csv_extract_pattern
            )
            
            # Compare files (only if not in BRELEARN mode)
            self.compare_files(file_paths, xml_path, csv_path)
            
            # Log success
            self.logger.MSWriteLog("info", f"Test {{self._testMethodName}} completed successfully")
            
            if TestConfig.BRELEARN:
                self.logger.MSWriteLog("info", f"Results saved to unique directory: {{unique_result_dir}}")
            
        except Exception as e:
            self.logger.MSWriteLog("error", f"Test failed: {{str(e)}}")
            raise
'''
            template += method_template
        
        # Add main execution block
        template += '''

# Example of how to run this specific test
if __name__ == "__main__":
    import unittest
    import asyncio
    
    # You can update configuration here before running the test
    # TestConfig.BRELEARN = True  # Enable learning mode
    # TestConfig.API_URL = "http://different-server:8034/IndianInvTally/process_doc"
    
    try:
        # Create a test logger for standalone execution
        from Test.CustomHelper import TestLogger
        import os
        
        os.makedirs(TestConfig.LOG_DIR, exist_ok=True)
        standalone_logger = TestLogger(TestConfig.LOG_DIR, "standalone_test.log")
        standalone_logger.MSWriteLog("info", "Starting standalone test execution")
        
        # Run the test
        asyncio.run(unittest.main(verbosity=2))
        standalone_logger.MSWriteLog("info", "Standalone test execution completed")
        
    except Exception as e:
        if 'standalone_logger' in locals():
            standalone_logger.MSWriteLog("error", f"Standalone test execution failed: {{str(e)}}")
        raise
'''
        
        return template
    
    def generate_test_config(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate test configuration for the migrated test.
        
        Args:
            analysis: Analysis results from existing test
            
        Returns:
            Test configuration dictionary
        """
        module_name = analysis['module_name']
        config = {}
        
        for test_method in analysis['test_methods']:
            method_config = {
                'doc_name': analysis['configurations'].get('doc_name', 'DefaultDoc'),
                'pdf_filename': analysis['configurations'].get('pdf_filename', 'default.pdf'),
                'xml_extract_pattern': analysis['configurations'].get('xml_extract_pattern', 'DName2512002397.xml'),
                'csv_extract_pattern': analysis['configurations'].get('csv_extract_pattern', 'Report')
            }
            config[test_method] = method_config
        
        return {module_name: config}
    
    def migrate_test(self, test_file_path: str) -> Tuple[str, Dict[str, Any]]:
        """
        Migrate a single test file.
        
        Args:
            test_file_path: Path to the test file to migrate
            
        Returns:
            Tuple of (new_file_content, test_config)
        """
        print(f"Migrating: {test_file_path}")
        
        # Analyze existing test
        analysis = self.analyze_test_file(test_file_path)
        
        # Generate new test file
        new_content = self.generate_new_test_file(analysis)
        
        # Generate test configuration
        test_config = self.generate_test_config(analysis)
        
        # Add to migration report
        self.migration_report.append({
            'original_file': test_file_path,
            'module_name': analysis['module_name'],
            'test_methods': analysis['test_methods'],
            'configurations': analysis['configurations']
        })
        
        return new_content, test_config
    
    def migrate_all_tests(self, output_suffix: str = "_migrated") -> Dict[str, Any]:
        """
        Migrate all discovered test files.
        
        Args:
            output_suffix: Suffix to add to migrated file names
            
        Returns:
            Combined test configuration for all migrated tests
        """
        existing_tests = self.discover_existing_tests()
        combined_config = {}
        
        print(f"Found {len(existing_tests)} test files to migrate:")
        for test_file in existing_tests:
            print(f"  - {test_file}")
        
        for test_file in existing_tests:
            try:
                new_content, test_config = self.migrate_test(test_file)
                
                # Write new test file
                base_name = os.path.splitext(test_file)[0]
                new_file_path = f"{base_name}{output_suffix}.py"
                
                with open(new_file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"  Created: {new_file_path}")
                
                # Combine configurations
                combined_config.update(test_config)
                
            except Exception as e:
                print(f"  Error migrating {test_file}: {str(e)}")
        
        return combined_config
    
    def print_migration_report(self):
        """Print a summary of the migration process."""
        print("\n" + "="*80)
        print("MIGRATION REPORT")
        print("="*80)
        
        for report in self.migration_report:
            print(f"\nFile: {report['original_file']}")
            print(f"Module: {report['module_name']}")
            print(f"Test Methods: {', '.join(report['test_methods'])}")
            print("Configurations:")
            for key, value in report['configurations'].items():
                print(f"  {key}: {value}")
        
        print(f"\nTotal files migrated: {len(self.migration_report)}")
        print("="*80)


def main():
    """Main function for test migration."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate existing tests to new framework")
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze existing tests')
    parser.add_argument('--output-suffix', default='_migrated', help='Suffix for migrated files')
    parser.add_argument('--update-config', action='store_true', help='Update test_config.py with new configurations')
    
    args = parser.parse_args()
    
    migrator = TestMigrator()
    
    if args.analyze_only:
        # Just analyze and report
        existing_tests = migrator.discover_existing_tests()
        for test_file in existing_tests:
            analysis = migrator.analyze_test_file(test_file)
            print(f"\nAnalysis for {test_file}:")
            print(f"  Module: {analysis['module_name']}")
            print(f"  Classes: {analysis['class_names']}")
            print(f"  Test Methods: {analysis['test_methods']}")
            print(f"  Configurations: {analysis['configurations']}")
    else:
        # Perform migration
        combined_config = migrator.migrate_all_tests(args.output_suffix)
        migrator.print_migration_report()
        
        if args.update_config:
            # Update TEST_DATA_CONFIGS
            TEST_DATA_CONFIGS.update(combined_config)
            print(f"\nUpdated TEST_DATA_CONFIGS with {len(combined_config)} new configurations")
            
            # Optionally save to file
            config_update = f"\n# Migrated configurations\nTEST_DATA_CONFIGS.update({combined_config})\n"
            with open("Test/migrated_config.py", 'w') as f:
                f.write(config_update)
            print("Saved configuration update to Test/migrated_config.py")


if __name__ == "__main__":
    main()
