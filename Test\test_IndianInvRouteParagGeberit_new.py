"""
Test Module: Indian Invoice Route - Parag Geberit

This module contains tests for processing Geberit invoices through the Indian Invoice Route API.
It uses the new modular test framework with automatic directory naming and unique result storage.
"""

import os
from Test.base_test import BaseIndianInvTest
from Test.test_config import TestConfig, get_test_data_config


class TestIndianInvRouteParagGeberit(BaseIndianInvTest):
    """
    Test class for Parag Geberit invoice processing.
    
    This class inherits from BaseIndianInvTest which provides all common functionality
    including automatic directory setup, logging, and file comparison utilities.
    """
    
    async def test_ParagGeberit(self):
        """
        Test the /process_doc endpoint for Geberit invoice processing.
        
        This test:
        1. Sends a PDF file to the API
        2. Receives a ZIP response
        3. Extracts XML and CSV files
        4. Renames files to standard names
        5. Compares with reference files (if not in BRELEARN mode)
        6. Stores results in unique directory to avoid overwriting
        """
        self.logger.MSWriteLog("info", f"Executing test: {self._testMethodName}")
        
        try:
            # Get test-specific configuration
            if not self.test_data_config:
                self.logger.MSWriteLog("error", "No test data configuration found")
                self.fail("No test data configuration found")
            
            doc_name = self.test_data_config['doc_name']
            pdf_filename = self.test_data_config['pdf_filename']
            xml_extract_pattern = self.test_data_config['xml_extract_pattern']
            csv_extract_pattern = self.test_data_config['csv_extract_pattern']
            
            # Verify license
            license_data = self.verify_license()
            
            # Get file paths (automatically uses module name for directories)
            file_paths = self.get_file_paths(doc_name, pdf_filename)
            
            # Verify input files exist
            self.verify_input_files(file_paths)
            
            # Check server availability
            if not self.is_server_listening():
                self.logger.MSWriteLog("error", f"Server at {TestConfig.API_URL} is not available")
                self.fail(f"Server at {TestConfig.API_URL} is not available")
            
            # Create unique result directory for this test run
            unique_result_dir = self.get_unique_result_directory(pdf_filename)
            
            # Update file paths to use unique result directory if in BRELEARN mode
            if TestConfig.BRELEARN:
                file_paths['output_dir'] = unique_result_dir
                file_paths['zip_file'] = os.path.join(unique_result_dir, "Content_zipFile.zip")
                file_paths['output_xml'] = os.path.join(unique_result_dir, f"{doc_name}.xml")
                file_paths['output_csv'] = os.path.join(unique_result_dir, f"Report_{doc_name}.csv")
            
            # Send API request
            response = await self.send_api_request(file_paths, pdf_filename, license_data)
            
            # Validate and save response
            self.validate_and_save_response(response, file_paths['zip_file'])
            
            # Extract and rename files
            xml_path, csv_path = self.extract_and_rename_files(
                file_paths['zip_file'],
                file_paths['output_dir'],
                doc_name,
                xml_extract_pattern,
                csv_extract_pattern
            )
            
            # Compare files (only if not in BRELEARN mode)
            self.compare_files(file_paths, xml_path, csv_path)
            
            # Log success
            self.logger.MSWriteLog("info", f"Test {self._testMethodName} completed successfully")
            
            if TestConfig.BRELEARN:
                self.logger.MSWriteLog("info", f"Results saved to unique directory: {unique_result_dir}")
            
        except Exception as e:
            self.logger.MSWriteLog("error", f"Test failed: {str(e)}")
            raise


# Example of how to run this specific test
if __name__ == "__main__":
    import unittest
    import asyncio
    
    # You can update configuration here before running the test
    # TestConfig.BRELEARN = True  # Enable learning mode
    # TestConfig.API_URL = "http://different-server:8034/IndianInvTally/process_doc"
    
    try:
        # Create a test logger for standalone execution
        from Test.CustomHelper import TestLogger
        import os
        
        os.makedirs(TestConfig.LOG_DIR, exist_ok=True)
        standalone_logger = TestLogger(TestConfig.LOG_DIR, "standalone_test.log")
        standalone_logger.MSWriteLog("info", "Starting standalone test execution")
        
        # Run the test
        asyncio.run(unittest.main(verbosity=2))
        standalone_logger.MSWriteLog("info", "Standalone test execution completed")
        
    except Exception as e:
        if 'standalone_logger' in locals():
            standalone_logger.MSWriteLog("error", f"Standalone test execution failed: {str(e)}")
        raise
