# Document Processing Guide

This guide outlines the steps to process documents i.e. how to retrive contents for a specific vendor and customer from MySQL database, run Python scripts to generate api responses using groq, and compare API responses.

## Step 1: Retrieve Documents
- Navigate to the document directory: `H:\AI Data\DailyData`.
- Locate the folder for the respective customer.
- Find and retrieve the documents for the specified vendor.

## Step 2: Access MySQL Workbench
- Open MySQL Workbench and check for the `dbvelocity` database.
- If `dbvelocity` is not present, import the latest version of the `dbvelocity` database.
- In the `dbvelocity` database, locate the `uploaded_docs` table.
- To find the `DocId` for a specific document, use the following SQL query:
  ```sql
  SELECT DocId FROM dbvelocity.uploaded_docs WHERE DocName LIKE '%documentnamewritehere%';
  ```

## Step 3: Process Documents Using DataExtractionByDocID.py
- Collect all `DocId`s retrieved from the database.
- Add the list of `DocId`s to the `DataExtractionByDocID.py` file located at `src\QuickTest\DataExtractionByDocID.py` in the Accuvelocity repository.
- Run the script using the command:
  ```bash
  python src\QuickTest\DataExtractionByDocID.py
  ```
- Provide the following inputs when prompted:
  - **Customer Name**: Enter the respective customer name.
  - **Vendor Name**: Enter the respective vendor name.
  - **Output Path**: Specify the path where the output documents will be saved.
- The script will create a folder structure at the specified output path: `[output_path]/[customer_name]/[vendor_name]`.
- For each `DocId`, three files will be generated:
  - `Objectresponse`
  - `Response`
  - `Usercontent`

## Step 4: Retrieve Prompt from Database
- In the `uploaded_docs` table, identify the `ModelId` for the specific vendor.
- Retrieve the prompt from the `prompt` table using the following SQL query:
  ```sql
  SELECT * FROM dbvelocity.prompt WHERE ModelId = [writeyourmodelid];
  ```
- Right-click on the `prompt` column, select "Open in Value Editor," and navigate to the "Text" tab.
- Copy the prompt content and save it as a `.txt` file at a path of your choice.

## Step 5: Configure and Run Test_GPTAPIV2.py
- Locate the `Test_GPTAPIV2.py` file at `src\QuickTest\Test_GPTAPIV2.py` in the Accuvelocity repository.
- Retrieve the response format file `GPTResponseFormat.json` from the path: `Data\Customer\[customer_name]\[vendor_name]\GPTResponseFormat.json`.
- Create a test case configuration in `lsTestCases` with the following structure:
  ```json
  {
      "strSystemPromptFile": "[path to prompt txt file]",
      "strUserContentFile": "[path to usercontent txt file from helper_fun.py output]",
      "dictResponseFormatFile": "[path to GPTResponseFormat.json]",
      "strClientName": "[customer name]",
      "strVoucherType": "",
      "strExtractAPIName": "[model name, e.g., GrokReasoning]",
      "reasoning_effort": "[low/high based on testing effort]",
      "bIsReasoningModel": true,
      "bIsGrokAPI": true,
      "max_tokens": 27000,
      "doc_id": [DocId]
  }
  ```
- Example configuration:
  ```json
  {
      "strSystemPromptFile": "H:\\DEVELOPER_PUBLIC\\interns\\sneha\\Prompt\\KohlerPrompt.txt",
      "strUserContentFile": "H:\\DEVELOPER_PUBLIC\\interns\\sneha\\QuickTestData\\ParagTraders\\Kohler\\strUserContent_4001.txt",
      "dictResponseFormatFile": "Data\\Customer\\17_ParagTraders\\4_Kohler\\GPTResponseFormat.json",
      "strClientName": "Parag Trader",
      "strVoucherType": "",
      "strExtractAPIName": "GrokReasoning",
      "reasoning_effort": "low",
      "bIsReasoningModel": true,
      "bIsGrokAPI": true,
      "max_tokens": 27000,
      "doc_id": 4001
  }
  ```
- Set the `output_dir` path to store the output data.
- Check the `.env` file in the Accuvelocity repository for the `GROK_API` key. If absent, add it.
- Install the `xai-sdk` module:
  ```bash
  pip install xai-sdk
  ```
- Run the script:
  ```bash
  python src\QuickTest\Test_GPTAPIV2.py
  ```

## Step 6: Compare GPT and Grok Responses
- Use the `jsonDiffComp.py` file to compare the GPT and Grok response JSON files.
- Update the following paths in `jsonDiffComp.py`:
  - `grok_dir`: Path where Grok responses are saved.
  - `gpt_dir`: Path where GPT responses are stored.
  - `output_path`: Path to save the output Excel file.
- Run the script to generate an Excel file comparing the responses.