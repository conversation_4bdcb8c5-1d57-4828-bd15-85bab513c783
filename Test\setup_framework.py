"""
Test Framework Setup Script

This script helps set up the new test framework by:
1. Creating necessary directories
2. Validating configuration
3. Checking dependencies
4. Providing setup guidance
"""

import os
import sys
import importlib
from typing import List, Dict, Any


class FrameworkSetup:
    """
    Setup utility for the test framework.
    """
    
    def __init__(self):
        self.test_dir = "Test"
        self.setup_issues = []
        self.setup_success = []
    
    def check_dependencies(self) -> List[str]:
        """
        Check if all required dependencies are available.
        
        Returns:
            List of missing dependencies
        """
        required_modules = [
            'unittest',
            'asyncio',
            'socket',
            'json',
            'httpx',
            'os',
            'sys',
            'glob',
            'datetime',
            'typing',
            'inspect'
        ]
        
        missing_modules = []
        
        for module in required_modules:
            try:
                importlib.import_module(module)
            except ImportError:
                missing_modules.append(module)
        
        return missing_modules
    
    def create_directories(self) -> List[str]:
        """
        Create all necessary directories for the framework.
        
        Returns:
            List of created directories
        """
        directories = [
            os.path.join(self.test_dir, "Input"),
            os.path.join(self.test_dir, "Reference"),
            os.path.join(self.test_dir, "Results"),
            os.path.join(self.test_dir, "Logs")
        ]
        
        created_dirs = []
        
        for directory in directories:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory, exist_ok=True)
                    created_dirs.append(directory)
                    self.setup_success.append(f"Created directory: {directory}")
                except Exception as e:
                    self.setup_issues.append(f"Failed to create directory {directory}: {str(e)}")
            else:
                self.setup_success.append(f"Directory already exists: {directory}")
        
        return created_dirs
    
    def check_framework_files(self) -> Dict[str, bool]:
        """
        Check if all framework files are present.
        
        Returns:
            Dict mapping file names to existence status
        """
        required_files = [
            'test_config.py',
            'base_test.py',
            'test_runner.py',
            'config_manager.py',
            'CustomHelper.py'
        ]
        
        file_status = {}
        
        for filename in required_files:
            file_path = os.path.join(self.test_dir, filename)
            exists = os.path.exists(file_path)
            file_status[filename] = exists
            
            if exists:
                self.setup_success.append(f"Framework file found: {filename}")
            else:
                self.setup_issues.append(f"Missing framework file: {filename}")
        
        return file_status
    
    def validate_configuration(self) -> List[str]:
        """
        Validate the test configuration.
        
        Returns:
            List of configuration issues
        """
        issues = []
        
        try:
            # Try to import configuration
            sys.path.insert(0, self.test_dir)
            from test_config import TestConfig, TEST_DATA_CONFIGS
            
            # Check license file
            if not os.path.exists(TestConfig.LICENSE_FILE):
                issues.append(f"License file not found: {TestConfig.LICENSE_FILE}")
            else:
                self.setup_success.append(f"License file found: {TestConfig.LICENSE_FILE}")
            
            # Check test data configurations
            if not TEST_DATA_CONFIGS:
                issues.append("No test data configurations found")
            else:
                self.setup_success.append(f"Found {len(TEST_DATA_CONFIGS)} test configurations")
                
                # Validate each configuration
                for module_name, module_config in TEST_DATA_CONFIGS.items():
                    for test_method, test_config in module_config.items():
                        required_keys = ['doc_name', 'pdf_filename', 'xml_extract_pattern', 'csv_extract_pattern']
                        for key in required_keys:
                            if key not in test_config:
                                issues.append(f"Missing key '{key}' in {module_name}.{test_method}")
            
        except ImportError as e:
            issues.append(f"Failed to import test configuration: {str(e)}")
        except Exception as e:
            issues.append(f"Configuration validation error: {str(e)}")
        
        return issues
    
    def check_existing_tests(self) -> Dict[str, Any]:
        """
        Check for existing test files and their status.
        
        Returns:
            Dict containing test file information
        """
        import glob
        
        test_files = glob.glob(os.path.join(self.test_dir, "test_*.py"))
        
        # Categorize test files
        framework_files = ['test_config.py', 'test_runner.py']
        old_tests = []
        new_tests = []
        
        for test_file in test_files:
            filename = os.path.basename(test_file)
            
            if filename in framework_files:
                continue
            
            # Check if it's using the new framework
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'BaseIndianInvTest' in content:
                    new_tests.append(test_file)
                else:
                    old_tests.append(test_file)
            except Exception:
                old_tests.append(test_file)
        
        return {
            'total_tests': len(test_files) - len(framework_files),
            'old_tests': old_tests,
            'new_tests': new_tests,
            'migration_needed': len(old_tests) > 0
        }
    
    def run_setup(self) -> bool:
        """
        Run the complete setup process.
        
        Returns:
            True if setup was successful, False otherwise
        """
        print("="*80)
        print("TEST FRAMEWORK SETUP")
        print("="*80)
        
        # Check dependencies
        print("\n1. Checking dependencies...")
        missing_deps = self.check_dependencies()
        if missing_deps:
            print(f"   ❌ Missing dependencies: {', '.join(missing_deps)}")
            self.setup_issues.extend([f"Missing dependency: {dep}" for dep in missing_deps])
        else:
            print("   ✅ All dependencies available")
            self.setup_success.append("All dependencies available")
        
        # Create directories
        print("\n2. Creating directories...")
        created_dirs = self.create_directories()
        if created_dirs:
            print(f"   ✅ Created {len(created_dirs)} directories")
        else:
            print("   ✅ All directories already exist")
        
        # Check framework files
        print("\n3. Checking framework files...")
        file_status = self.check_framework_files()
        missing_files = [f for f, exists in file_status.items() if not exists]
        if missing_files:
            print(f"   ❌ Missing files: {', '.join(missing_files)}")
        else:
            print("   ✅ All framework files present")
        
        # Validate configuration
        print("\n4. Validating configuration...")
        config_issues = self.validate_configuration()
        if config_issues:
            print(f"   ❌ Configuration issues found: {len(config_issues)}")
            self.setup_issues.extend(config_issues)
        else:
            print("   ✅ Configuration is valid")
        
        # Check existing tests
        print("\n5. Checking existing tests...")
        test_info = self.check_existing_tests()
        print(f"   📊 Total test files: {test_info['total_tests']}")
        print(f"   📊 New framework tests: {len(test_info['new_tests'])}")
        print(f"   📊 Old tests needing migration: {len(test_info['old_tests'])}")
        
        if test_info['migration_needed']:
            print("   ⚠️  Some tests need migration to new framework")
        else:
            print("   ✅ All tests use new framework")
        
        # Summary
        print("\n" + "="*80)
        print("SETUP SUMMARY")
        print("="*80)
        
        if self.setup_issues:
            print(f"\n❌ Issues found ({len(self.setup_issues)}):")
            for issue in self.setup_issues:
                print(f"   - {issue}")
        
        if self.setup_success:
            print(f"\n✅ Successful items ({len(self.setup_success)}):")
            for success in self.setup_success[:5]:  # Show first 5
                print(f"   - {success}")
            if len(self.setup_success) > 5:
                print(f"   ... and {len(self.setup_success) - 5} more")
        
        # Recommendations
        print("\n📋 NEXT STEPS:")
        
        if missing_deps:
            print("   1. Install missing dependencies:")
            for dep in missing_deps:
                print(f"      pip install {dep}")
        
        if missing_files:
            print("   2. Ensure all framework files are present")
        
        if config_issues:
            print("   3. Fix configuration issues:")
            for issue in config_issues[:3]:
                print(f"      - {issue}")
        
        if test_info['migration_needed']:
            print("   4. Migrate old tests:")
            print("      python Test/migrate_tests.py")
        
        print("   5. Run tests:")
        print("      python Test/test_runner.py --summary")
        print("      python Test/test_runner.py")
        
        print("\n📖 For detailed documentation, see: Test/README.md")
        
        return len(self.setup_issues) == 0
    
    def create_sample_test(self):
        """Create a sample test file to demonstrate the framework."""
        sample_content = '''"""
Sample Test Module

This is a sample test to demonstrate the new test framework.
"""

from Test.base_test import BaseIndianInvTest
from Test.test_config import TestConfig


class TestSample(BaseIndianInvTest):
    """
    Sample test class demonstrating the new framework.
    """
    
    async def test_sample_method(self):
        """
        Sample test method.
        
        This is a placeholder test that demonstrates the framework structure.
        Replace this with your actual test logic.
        """
        self.logger.MSWriteLog("info", f"Executing sample test: {self._testMethodName}")
        
        # This is where your test logic would go
        # For now, just log that the test ran
        self.logger.MSWriteLog("info", "Sample test completed successfully")
        
        # You can access test configuration like this:
        # if self.test_data_config:
        #     doc_name = self.test_data_config['doc_name']
        #     pdf_filename = self.test_data_config['pdf_filename']


if __name__ == "__main__":
    import unittest
    import asyncio
    
    asyncio.run(unittest.main(verbosity=2))
'''
        
        sample_file = os.path.join(self.test_dir, "test_sample.py")
        
        if not os.path.exists(sample_file):
            with open(sample_file, 'w', encoding='utf-8') as f:
                f.write(sample_content)
            print(f"   ✅ Created sample test: {sample_file}")
        else:
            print(f"   ℹ️  Sample test already exists: {sample_file}")


def main():
    """Main function for setup script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Set up the test framework")
    parser.add_argument('--create-sample', action='store_true', help='Create a sample test file')
    parser.add_argument('--quiet', '-q', action='store_true', help='Minimal output')
    
    args = parser.parse_args()
    
    setup = FrameworkSetup()
    
    if not args.quiet:
        success = setup.run_setup()
    else:
        # Quick setup without detailed output
        setup.create_directories()
        setup.check_framework_files()
        config_issues = setup.validate_configuration()
        success = len(setup.setup_issues) == 0 and len(config_issues) == 0
        
        if success:
            print("✅ Framework setup completed successfully")
        else:
            print("❌ Framework setup encountered issues")
    
    if args.create_sample:
        print("\n6. Creating sample test...")
        setup.create_sample_test()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
