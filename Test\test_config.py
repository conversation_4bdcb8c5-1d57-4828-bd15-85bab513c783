"""
Global Test Configuration Module

This module contains all the global configuration settings for the test framework.
It provides centralized management of test parameters, file paths, and test data.
"""

import os
import inspect
from datetime import datetime
from typing import Dict, List, Optional, Any


class TestConfig:
    """Global configuration class for all tests."""
    
    # Base directories
    TEST_DIR = "Test"
    BASE_INPUT_DIR = os.path.join(TEST_DIR, "Input")
    BASE_REFERENCE_DIR = os.path.join(TEST_DIR, "Reference")
    BASE_RESULTS_DIR = os.path.join(TEST_DIR, "Results")
    LOG_DIR = os.path.join(TEST_DIR, "Logs")
    
    # API Configuration
    API_URL = "http://**************:8034/IndianInvTally/process_doc"
    API_HOST = "**************"
    API_PORT = 8034
    API_TIMEOUT = 1200
    
    # License Configuration
    LICENSE_FILE = os.path.join(TEST_DIR, "license.lic")
    
    # Test Modes
    BRELEARN = False  # Set to True for learning mode, False for comparison mode
    
    # API Request Configuration
    API_CONFIG = {
        "bTestMode": True,
        "strVoucherType": "PV_WITH_INVENTORY",
        "bIsMultivendorDoc": False,
        "strSerializeUserConfig": {
            "Exe_version": "2.4",
            "Exe_ReleaseDate": "2025-06-28",
            "Tdl_version": "8.0",
            "Tdl_ReleaseDate": "2025-03-29",
            "worker": 2,
            "apiEndpoints": [
                "http://*************:8024/",
                "http://**************:8034/",
                "http://*************:8034/",
                "http://**************:8024/"
            ]
        }
    }
    
    # File comparison settings
    XML_IGNORE_TAGS = ["VOUCHERNUMBER"]
    CSV_IGNORE_COLUMNS = ["Received Date"]
    
    @classmethod
    def get_test_module_name(cls, test_file_path: Optional[str] = None) -> str:
        """
        Get the test module name from the calling test file.
        
        Args:
            test_file_path: Optional path to test file. If None, auto-detects from call stack.
            
        Returns:
            str: Test module name (e.g., 'test_IndianInvRouteParagGeberit')
        """
        if test_file_path:
            return os.path.splitext(os.path.basename(test_file_path))[0]
        
        # Auto-detect from call stack
        frame = inspect.currentframe()
        try:
            # Go up the call stack to find the test file
            while frame:
                filename = frame.f_code.co_filename
                if filename.endswith('.py') and 'test_' in os.path.basename(filename):
                    return os.path.splitext(os.path.basename(filename))[0]
                frame = frame.f_back
        finally:
            del frame
        
        raise ValueError("Could not determine test module name from call stack")
    
    @classmethod
    def get_test_directories(cls, test_module_name: str) -> Dict[str, str]:
        """
        Get all directory paths for a test module.
        
        Args:
            test_module_name: Name of the test module
            
        Returns:
            Dict containing all directory paths
        """
        return {
            'input_dir': os.path.join(cls.BASE_INPUT_DIR, test_module_name),
            'reference_dir': os.path.join(cls.BASE_REFERENCE_DIR, test_module_name),
            'results_dir': os.path.join(cls.BASE_RESULTS_DIR, test_module_name),
            'log_dir': cls.LOG_DIR
        }
    
    @classmethod
    def get_unique_result_dir(cls, test_module_name: str, test_method_name: str, 
                             file_identifier: str = None) -> str:
        """
        Generate a unique result directory to avoid overwriting previous results.
        
        Args:
            test_module_name: Name of the test module
            test_method_name: Name of the test method
            file_identifier: Optional identifier for the file being tested
            
        Returns:
            str: Unique result directory path
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if file_identifier:
            # Create identifier from file name/path
            safe_identifier = file_identifier.replace('/', '_').replace('\\', '_').replace('.', '_')
            result_subdir = f"{test_method_name}_{safe_identifier}_{timestamp}"
        else:
            result_subdir = f"{test_method_name}_{timestamp}"
        
        return os.path.join(cls.BASE_RESULTS_DIR, test_module_name, result_subdir)
    
    @classmethod
    def get_log_file_name(cls, test_module_name: str) -> str:
        """
        Get the log file name for a test module.
        
        Args:
            test_module_name: Name of the test module
            
        Returns:
            str: Log file name
        """
        return f"{test_module_name}.log"


class TestCaseConfig:
    """Configuration for individual test cases."""
    
    def __init__(self, test_module_name: str = None):
        """
        Initialize test case configuration.
        
        Args:
            test_module_name: Optional test module name. Auto-detected if not provided.
        """
        self.test_module_name = test_module_name or TestConfig.get_test_module_name()
        self.directories = TestConfig.get_test_directories(self.test_module_name)
        self.log_file = TestConfig.get_log_file_name(self.test_module_name)
        
        # Test-specific configurations (can be overridden per test)
        self.test_files = {}  # Will store test-specific file configurations
        
    def add_test_file_config(self, test_method_name: str, config: Dict[str, Any]):
        """
        Add configuration for a specific test file.
        
        Args:
            test_method_name: Name of the test method
            config: Configuration dictionary containing file paths and parameters
        """
        self.test_files[test_method_name] = config
    
    def get_test_file_config(self, test_method_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific test file.
        
        Args:
            test_method_name: Name of the test method
            
        Returns:
            Dict containing test file configuration
        """
        return self.test_files.get(test_method_name, {})


# Test Data Configurations
# Each test module can define its specific test data here
TEST_DATA_CONFIGS = {
    'test_IndianInvRouteParagGeberit': {
        'test_ParagGeberit': {
            'doc_name': 'ParagGEBERIT',
            'pdf_filename': 'GEBERIT.pdf',
            'xml_extract_pattern': 'DName2512002397.xml',
            'csv_extract_pattern': 'Report'
        }
    },
    'test_IndianInvRouteParagToto': {
        'test_ParagToto': {
            'doc_name': 'ParagTOTO',
            'pdf_filename': 'TOTO.pdf',
            'xml_extract_pattern': 'DName2512002397.xml',
            'csv_extract_pattern': 'Report'
        }
    },
    'test_IndianInvRouteParagSimpolo': {
        'test_ParagSimpolo': {
            'doc_name': 'ParagSIMPOLO',
            'pdf_filename': 'SIMPOLO.pdf',
            'xml_extract_pattern': 'DName2512002397.xml',
            'csv_extract_pattern': 'Report'
        }
    },
    'test_IndianInvRouteParagKohler': {
        'test_ParagKohler': {
            'doc_name': 'ParagKOHLER',
            'pdf_filename': 'KOHLER.pdf',
            'xml_extract_pattern': 'DName2512002397.xml',
            'csv_extract_pattern': 'Report'
        }
    },
    'test_IndianInvRouteParagHansgrohe': {
        'test_ParagHansgrohe': {
            'doc_name': 'ParagHANSGROHE',
            'pdf_filename': 'HANSGROHE.pdf',
            'xml_extract_pattern': 'DName2512002397.xml',
            'csv_extract_pattern': 'Report'
        }
    },
    # Add more test configurations here as needed
}


def get_test_data_config(test_module_name: str, test_method_name: str) -> Dict[str, Any]:
    """
    Get test data configuration for a specific test.
    
    Args:
        test_module_name: Name of the test module
        test_method_name: Name of the test method
        
    Returns:
        Dict containing test data configuration
    """
    module_config = TEST_DATA_CONFIGS.get(test_module_name, {})
    return module_config.get(test_method_name, {})
