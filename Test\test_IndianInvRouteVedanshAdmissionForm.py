import unittest
import os
import sys
import asyncio
import zipfile
import xml.etree.ElementTree as ET
import socket
import json
import hashlib
import base64
import traceback
import logging
import binascii
import csv

# Setup console logger for early failures
logging.basicConfig(level=logging.DEBUG, handlers=[
    logging.StreamHandler(sys.stdout),
    logging.FileHandler("test_debug.log")
])
console_logger = logging.getLogger(__name__)
console_logger.debug("Starting test script execution")

# Dependency checks
try:
    import httpx
    console_logger.debug("httpx imported successfully")
except ImportError:
    console_logger.error("The 'httpx' library is not installed. Please install it using 'pip install httpx'.")
    raise ImportError("The 'httpx' library is not installed. Please install it using 'pip install httpx'.")
try:
    from cryptography.fernet import Fernet
    console_logger.debug("cryptography imported successfully")
except ImportError:
    console_logger.error("The 'cryptography' library is not installed. Please install it using 'pip install cryptography'.")
    raise ImportError("The 'cryptography' library is not installed. Please install it using 'pip install cryptography'.")
try:
    import jwt
    console_logger.debug("pyjwt imported successfully")
except ImportError:
    console_logger.error("The 'pyjwt' library is not installed. Please install it using 'pip install pyjwt'.")
    raise ImportError("The 'pyjwt' library is not installed. Please install it using 'pip install pyjwt'.")

print("Dependencies imported successfully")

# Log sys.path for debugging
console_logger.debug(f"sys.path: {sys.path}")
print(f"sys.path: {sys.path}")

# Create log directory early
log_dir = os.path.join("Test", "Input", "test_IndianInvRouteVedanshAdmissionForm", "test_VedanshAdmissionForm")
try:
    os.makedirs(log_dir, exist_ok=True)
    if os.path.exists(log_dir):
        console_logger.debug(f"Log directory created or exists: {log_dir}")
        print(f"Log directory created or exists: {log_dir}")
    else:
        console_logger.error(f"Failed to verify log directory: {log_dir}")
        print(f"Failed to verify log directory: {log_dir}")
        raise RuntimeError(f"Failed to verify log directory: {log_dir}")
except Exception as e:
    console_logger.error(f"Failed to create log directory {log_dir}: {str(e)}")
    console_logger.error(f"Traceback: {traceback.format_exc()}")
    print(f"Failed to create log directory {log_dir}: {str(e)}")
    raise RuntimeError(f"Failed to create log directory {log_dir}: {str(e)}")

# Temporary standard logger (TestLogger)
class TestLogger:
    def __init__(self, log_dir, log_file):
        self.logger = logging.getLogger("TestLogger")
        self.logger.setLevel(logging.DEBUG)
        file_handler = logging.FileHandler(os.path.join(log_dir, log_file))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        self.logger.addHandler(file_handler)
        self.logger.addHandler(logging.StreamHandler(sys.stdout))

    def MSWriteLog(self, level, message):
        if level.lower() == "info":
            self.logger.info(message)
        elif level.lower() == "error":
            self.logger.error(message)
        elif level.lower() == "debug":
            self.logger.debug(message)
        else:
            self.logger.warning(f"Unknown log level {level}: {message}")

# Initialize temporary logger
test_logger = TestLogger(log_dir, "test_IndianInvRouteVedanshAdmissionForm.log")
test_logger.MSWriteLog("info", "Temporary logger initialized")

SECRET_KEY = base64.urlsafe_b64encode(b'ea7634b4c4b4c23a42218d4b0c814869')

class CLicenseHelper:
    @staticmethod
    def MSVerifyLicense(license_file):
        """
        Verify the license file and extract the JWT token.
        Args:
            license_file (str): Path to the license file.
        Returns:
            dict: Dictionary containing license data with 'Token' field.
        Raises:
            FileNotFoundError: If license file is not found.
            Exception: If decryption or JWT decoding fails.
        """
        try:
            console_logger.debug(f"Verifying license file: {license_file}")
            print(f"Verifying license file: {license_file}")
            if not os.path.exists(license_file):
                test_logger.MSWriteLog("error", f"License file {license_file} not found")
                raise FileNotFoundError(f"License file {license_file} not found")
            with open(license_file, "rb") as lic_file:
                encrypted_license = lic_file.read()
            cipher_suite = Fernet(SECRET_KEY)
            token = cipher_suite.decrypt(encrypted_license).decode("utf-8")
            dictLicenseData = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
            dictLicenseData["Token"] = token
            test_logger.MSWriteLog("info", f"License verification succeeded for {license_file}")
            test_logger.MSWriteLog("debug", f"JWT payload: {dictLicenseData}")
            return dictLicenseData
        except Exception as e:
            test_logger.MSWriteLog("error", f"License verification failed: {str(e)}")
            test_logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            console_logger.error(f"License verification failed: {str(e)}")
            print(f"License verification failed: {str(e)}")
            raise e

def calculate_checksum(file_path, algorithm="md5"):
    """
    Calculate checksum of a file using the specified algorithm.
    Args:
        file_path (str): Path to the file.
        algorithm (str): Hash algorithm ('md5', 'sha256', 'sha1').
    Returns:
        str: Hexadecimal checksum.
    """
    try:
        file_size = os.path.getsize(file_path)
        test_logger.MSWriteLog("debug", f"File size for {file_path}: {file_size} bytes")
        console_logger.debug(f"File size for {file_path}: {file_size} bytes")
        print(f"File size for {file_path}: {file_size} bytes")
        with open(file_path, "rb") as f:
            first_bytes = f.read(16)
            test_logger.MSWriteLog("debug", f"First 16 bytes of {file_path}: {binascii.hexlify(first_bytes).decode()}")
            console_logger.debug(f"First 16 bytes of {file_path}: {binascii.hexlify(first_bytes).decode()}")
            print(f"First 16 bytes of {file_path}: {binascii.hexlify(first_bytes).decode()}")
        hash_obj = hashlib.md5() if algorithm == "md5" else hashlib.sha256() if algorithm == "sha256" else hashlib.sha1()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        checksum = hash_obj.hexdigest()
        test_logger.MSWriteLog("debug", f"Calculated {algorithm.upper()} checksum for {file_path}: {checksum}")
        console_logger.debug(f"Calculated {algorithm.upper()} checksum for {file_path}: {checksum}")
        print(f"Calculated {algorithm.upper()} checksum for {file_path}: {checksum}")
        for algo in ["md5", "sha256", "sha1"]:
            if algo != algorithm:
                hash_obj = hashlib.md5() if algo == "md5" else hashlib.sha256() if algo == "sha256" else hashlib.sha1()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_obj.update(chunk)
                algo_checksum = hash_obj.hexdigest()
                test_logger.MSWriteLog("debug", f"Calculated {algo.upper()} checksum for {file_path}: {algo_checksum}")
                console_logger.debug(f"Calculated {algo.upper()} checksum for {file_path}: {algo_checksum}")
                print(f"Calculated {algo.upper()} checksum for {file_path}: {algo_checksum}")
        return checksum
    except Exception as e:
        test_logger.MSWriteLog("error", f"Checksum calculation failed for {file_path}: {str(e)}")
        console_logger.error(f"Checksum calculation failed for {file_path}: {str(e)}")
        print(f"Checksum calculation failed for {file_path}: {str(e)}")
        raise

def compare_xml(logger, file1, file2, ignore_tags=None):
    """
    Compare two XML files, ignoring specified tags.
    Args:
        logger: Logger instance for logging.
        file1 (str): Path to the input XML file.
        file2 (str): Path to the generated XML file.
        ignore_tags (list): List of tags to ignore during comparison.
    Returns:
        tuple: (bAreSame, strLogMessage) where bAreSame is a boolean indicating if XMLs match,
               and strLogMessage is a descriptive message.
    """
    if ignore_tags is None:
        ignore_tags = []
    try:
        tree1 = ET.parse(file1)
        tree2 = ET.parse(file2)
        root1 = tree1.getroot()
        root2 = tree2.getroot()
        for tag in ignore_tags:
            for elem in root1.findall(f".//{tag}"):
                elem.clear()
            for elem in root2.findall(f".//{tag}"):
                elem.clear()
        xml1_str = ET.tostring(root1, encoding="unicode")
        xml2_str = ET.tostring(root2, encoding="unicode")
        bAreSame = xml1_str == xml2_str
        strLogMessage = "XML files match" if bAreSame else "XML files differ after ignoring specified tags"
        logger.MSWriteLog("info", strLogMessage)
        return bAreSame, strLogMessage
    except Exception as e:
        strLogMessage = f"Error comparing XML files: {str(e)}"
        logger.MSWriteLog("error", strLogMessage)
        logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
        return False, strLogMessage

def compare_csv(logger, file1, file2, ignore_columns=None):
    """
    Compare two CSV files for identical content, ignoring specified columns.
    Args:
        logger: Logger instance for logging.
        file1 (str): Path to the input CSV file.
        file2 (str): Path to the generated CSV file.
        ignore_columns (list): List of column names to ignore during comparison.
    Returns:
        tuple: (bAreSame, strLogMessage) where bAreSame is a boolean indicating if CSVs match,
               and strLogMessage is a descriptive message.
    """
    if ignore_columns is None:
        ignore_columns = []
    try:
        with open(file1, 'r', newline='') as f1, open(file2, 'r', newline='') as f2:
            reader1 = csv.reader(f1)
            reader2 = csv.reader(f2)
            
            # Read headers
            headers1 = next(reader1, None)
            headers2 = next(reader2, None)
            if headers1 is None or headers2 is None:
                strLogMessage = "One or both CSV files are empty or missing headers"
                logger.MSWriteLog("error", strLogMessage)
                return False, strLogMessage
            
            # Find indices of columns to ignore
            ignore_indices = []
            for col in ignore_columns:
                if col in headers1:
                    idx = headers1.index(col)
                    if idx not in ignore_indices:
                        ignore_indices.append(idx)
                        logger.MSWriteLog("info", f"Ignoring column '{col}' at index {idx} in file1")
                if col in headers2:
                    idx = headers2.index(col)
                    if idx not in ignore_indices:
                        ignore_indices.append(idx)
                        logger.MSWriteLog("info", f"Ignoring column '{col}' at index {idx} in file2")
            
            # Read and filter rows
            csv1 = [[val for i, val in enumerate(row) if i not in ignore_indices] for row in [headers1] + list(reader1)]
            csv2 = [[val for i, val in enumerate(row) if i not in ignore_indices] for row in [headers2] + list(reader2)]
            
            # Compare filtered CSVs
            bAreSame = csv1 == csv2
            strLogMessage = "CSV files match after ignoring specified columns" if bAreSame else "CSV files differ after ignoring specified columns"
            logger.MSWriteLog("info", strLogMessage)
            if not bAreSame:
                logger.MSWriteLog("debug", f"Filtered CSV1 content: {csv1}")
                logger.MSWriteLog("debug", f"Filtered CSV2 content: {csv2}")
            return bAreSame, strLogMessage
    except Exception as e:
        strLogMessage = f"Error comparing CSV files: {str(e)}"
        logger.MSWriteLog("error", strLogMessage)
        logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
        return False, strLogMessage

class TestIndianInvRouteAbhinavPO(unittest.IsolatedAsyncioTestCase):
    # Default settings
    bRelearn = False
    strTestDir = "Test"
    current_module_name = os.path.splitext(os.path.basename(__file__))[0]
    strInputDirectory = os.path.join(strTestDir, "Input", current_module_name)
    strReferenceDirectory = os.path.join(strTestDir, "Reference", current_module_name)
    strTargetDirectory = strInputDirectory
    api_url = "http://**************:8034/IndianInvTally/process_doc"
    license_file = os.path.join(strTestDir, "license.lic")

    async def asyncSetUp(self):
        """
        Async method to set up the test case.
        Creates all necessary directories and initializes logging.
        """
        try:
            self.strCurTestCaseDirPath = os.path.join(self.strInputDirectory, self._testMethodName)
            os.makedirs(self.strInputDirectory, exist_ok=True)
            os.makedirs(os.path.join(self.strReferenceDirectory, self._testMethodName), exist_ok=True)
            os.makedirs(self.strCurTestCaseDirPath, exist_ok=True)
            self.logger = test_logger
            test_logger.MSWriteLog("info", f"Setup is done for: {self._testMethodName}")
            console_logger.debug(f"Setup completed for: {self._testMethodName}")
            print(f"Setup completed for: {self._testMethodName}")
        except Exception as e:
            console_logger.error(f"Setup failed: {str(e)}")
            print(f"Setup failed: {str(e)}")
            test_logger.MSWriteLog("error", f"Setup failed: {str(e)}")
            test_logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            raise e

    async def asyncTearDown(self):
        """
        Async method to clean up after the test case.
        Logs cleanup.
        """
        try:
            test_logger.MSWriteLog("info", f"Test case directory {self.strCurTestCaseDirPath} cleaned up.")
            console_logger.debug(f"Teardown completed for: {self.strCurTestCaseDirPath}")
            print(f"Teardown completed for: {self.strCurTestCaseDirPath}")
        except Exception as e:
            console_logger.error(f"Teardown failed: {str(e)}")
            print(f"Teardown failed: {str(e)}")
            test_logger.MSWriteLog("error", f"Error during cleanup: {str(e)}")
            test_logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            raise e

    def is_server_listening(self, host="**************", port=8034, timeout=2):
        """
        Check if the server is listening on the specified host and port.
        Args:
            host (str): Server host.
            port (int): Server port.
            timeout (int): Timeout for connection attempt.
        Returns:
            bool: True if server is listening, False otherwise.
        """
        try:
            with socket.create_connection((host, port), timeout=timeout):
                test_logger.MSWriteLog("info", f"Server at {host}:{port} is listening.")
                console_logger.debug(f"Server check: {host}:{port} is listening")
                print(f"Server check: {host}:{port} is listening")
                return True
        except (socket.timeout, ConnectionRefusedError) as e:
            test_logger.MSWriteLog("error", f"Server at {host}:{port} is not listening: {str(e)}")
            test_logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            console_logger.error(f"Server check failed: {host}:{port} - {str(e)}")
            print(f"Server check failed: {host}:{port} - {str(e)}")
            return False

    async def test_VedanshAdmissionForm(self):
        """
        Test case to verify the /process_doc endpoint for a premium user (iUserid=4).
        Sends a PDF file, receives a ZIP, extracts XML and CSV, and compares with input XML and CSV,
        ignoring specified tags and columns.
        """
        test_logger.MSWriteLog("info", f"Executing test method: {self._testMethodName}")
        console_logger.debug(f"Starting test: {self._testMethodName}")
        print(f"Starting test: {self._testMethodName}")
        try:
            # Verify license and get token
            if not os.path.exists(self.license_file):
                test_logger.MSWriteLog("error", f"License file not found: {self.license_file}")
                self.fail(f"License file not found: {self.license_file}")
            license_data = CLicenseHelper.MSVerifyLicense(self.license_file)
            strUserToken = license_data["Token"]
            user_id = license_data.get("uid", "unknown")
            test_logger.MSWriteLog("info", f"License verified, token obtained: {strUserToken[:10]}..., uid: {user_id}")
            console_logger.debug(f"License verified, token obtained: {strUserToken[:10]}..., uid: {user_id}")
            print(f"License verified, token obtained: {strUserToken[:10]}..., uid: {user_id}")

            # Define file paths
            strInputPdfPath = os.path.join(self.strCurTestCaseDirPath, "844-847 1.7.25_PG2To2_TS102059.pdf")
            strZipPath = os.path.join(self.strReferenceDirectory, self._testMethodName, "Content_zipFile.zip")
            strInputXmlPath = os.path.join(self.strCurTestCaseDirPath, "REQ_CSAVDEVELOPER_TS114157_UID5OM3KL_DID5788_DNameWMPR25001596979.xml")
            strInputCsvPath = os.path.join(self.strCurTestCaseDirPath, "Report_20250718_114540_PV-WITHOUT-INVENTORY_V1.csv")
            test_logger.MSWriteLog("info", f"Using input PDF file: {strInputPdfPath}")
            test_logger.MSWriteLog("info", f"ZIP file will be saved to: {strZipPath}")
            test_logger.MSWriteLog("info", f"Input XML file: {strInputXmlPath}")
            test_logger.MSWriteLog("info", f"Input CSV file: {strInputCsvPath}")
            console_logger.debug(f"Input PDF file: {strInputPdfPath}")
            console_logger.debug(f"ZIP file will be saved to: {strZipPath}")
            console_logger.debug(f"Input XML file: {strInputXmlPath}")
            console_logger.debug(f"Input CSV file: {strInputCsvPath}")
            print(f"Input PDF file: {strInputPdfPath}")
            print(f"ZIP file will be saved to: {strZipPath}")
            print(f"Input XML file: {strInputXmlPath}")
            print(f"Input CSV file: {strInputCsvPath}")

            # Verify input files exist
            # if not os.path.exists(strInputPdfPath):
            #     test_logger.MSWriteLog("error", f"Input PDF file not found: {strInputPdfPath}")
            #     self.fail(f"Input PDF file not found: {strInputPdfPath}")
            # if not os.path.exists(strInputXmlPath):
            #     test_logger.MSWriteLog("error", f"Input XML file not found: {strInputXmlPath}")
            #     self.fail(f"Input XML file not found: {strInputXmlPath}")
            # if not os.path.exists(strInputCsvPath):
            #     test_logger.MSWriteLog("error", f"Input CSV file not found: {strInputCsvPath}")
            #     self.fail(f"Input CSV file not found: {strInputCsvPath}")

            # Ensure Reference directory exists
            os.makedirs(os.path.dirname(strZipPath), exist_ok=True)

            # Check if server is listening
            if not self.is_server_listening():
                test_logger.MSWriteLog("error", f"Server at {self.api_url} is not available.")
                self.fail(f"Server at {self.api_url} is not available.")

            # Prepare API call parameters
            headers = {
                "Authorization": f"Bearer {strUserToken}",
                "iUserid": str(user_id)
            }
            checksum = calculate_checksum(strInputPdfPath, algorithm="md5")
            lsClientDocMetaData = [{
                "filename": "844-847 1.7.25_PG2To2_TS102059.pdf",
                "Type": ".pdf",
                "location": strInputPdfPath,
                "checksum": checksum
            }]
            checksums = [checksum]
            params = {
                "bTestMode": True,
                "strVoucherType": "RECEIPT_NOTE",
                "lsClientDocMetaData": json.dumps(lsClientDocMetaData),
                "bIsMultivendorDoc": False,
                "strSystemName": os.getlogin(),
                "strSerializeUserConfig": json.dumps({
                    "Exe_version": "2.4",
                    "Exe_ReleaseDate": "2025-06-28",
                    "Tdl_version": "8.0",
                    "Tdl_ReleaseDate": "2025-03-29",
                    "worker": 2,
                    "apiEndpoints": [
                        "http://*************:8024/",
                        "http://**************:8034/",
                        "http://*************:8034/",
                        "http://**************:8024/"
                    ]
                })
            }
            data = {"checksums": ",".join(checksums)}

            # Log request details
            test_logger.MSWriteLog("debug", f"Request headers: {headers}")
            test_logger.MSWriteLog("debug", f"Request params: {params}")
            test_logger.MSWriteLog("debug", f"Request files metadata: {lsClientDocMetaData}")
            test_logger.MSWriteLog("debug", f"Request data: {data}")
            console_logger.debug(f"Request headers: {headers}")
            console_logger.debug(f"Request params: {params}")
            console_logger.debug(f"Request files metadata: {lsClientDocMetaData}")
            console_logger.debug(f"Request data: {data}")
            print(f"Request headers: {headers}")
            print(f"Request params: {params}")
            print(f"Request files metadata: {lsClientDocMetaData}")
            print(f"Request data: {data}")

            # Send POST request to the live server
            test_logger.MSWriteLog("info", f"Sending POST request to {self.api_url}")
            console_logger.debug(f"Sending POST request to {self.api_url}")
            print(f"Sending POST request to {self.api_url}")
            try:
                with httpx.Client(timeout=1200) as client:
                    response = client.post(
                        self.api_url,
                        headers=headers,
                        params=params,
                        files=[("documents", ("844-847 1.7.25_PG2To2_TS102059.pdf", open(strInputPdfPath, "rb"), "application/pdf"))],
                        data=data
                    )
            except Exception as e:
                test_logger.MSWriteLog("error", f"API request failed: {str(e)}")
                test_logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
                console_logger.error(f"API request failed: {str(e)}")
                print(f"API request failed: {str(e)}")
                raise

            # Log response details
            test_logger.MSWriteLog("info", f"Received response with status code: {response.status_code}")
            console_logger.debug(f"Received response: status code {response.status_code}")
            print(f"Received response: status code {response.status_code}")
            try:
                response_content = response.json()
            except ValueError:
                response_content = response.text
            test_logger.MSWriteLog("debug", f"Response content: {response_content}")
            console_logger.debug(f"Response content: {response_content}")
            print(f"Response content: {response_content}")

            if response.status_code != 200:
                test_logger.MSWriteLog("error", f"Non-200 response: {response.status_code}, content: {response_content}")
                console_logger.error(f"Non-200 response: {response.status_code}, content: {response_content}")
                print(f"Non-200 response: {response.status_code}, content: {response_content}")
                self.fail(f"Expected status code 200, got {response.status_code}. Response content: {response_content}")

            self.assertEqual(response.headers["Content-Type"], "application/zip", f"Expected Content-Type application/zip, got {response.headers['Content-Type']}")
            test_logger.MSWriteLog("info", f"Response Content-Type: {response.headers['Content-Type']}")
            console_logger.debug(f"Response Content-Type: {response.headers['Content-Type']}")
            print(f"Response Content-Type: {response.headers['Content-Type']}")

            # Verify checksum
            response_checksum = response.headers.get("X-Zip-Checksum")
            if not response_checksum:
                test_logger.MSWriteLog("error", "Checksum not received in response headers.")
                self.fail("Checksum not received in response headers.")

            # Save ZIP response to Reference directory
            with open(strZipPath, "wb") as f:
                for chunk in response.iter_bytes():
                    f.write(chunk)
            saved_checksum = calculate_checksum(strZipPath, algorithm="md5")
            self.assertEqual(saved_checksum, response_checksum, "Checksum mismatch! The downloaded file is corrupted.")
            self.assertTrue(os.path.exists(strZipPath), f"ZIP file not found at: {strZipPath}")
            test_logger.MSWriteLog("info", f"ZIP file saved at: {strZipPath}")
            console_logger.debug(f"ZIP file saved at: {strZipPath}")
            print(f"ZIP file saved at: {strZipPath}")

            # Extract XML and CSV from ZIP to Reference directory
            xml_filename = None
            csv_filename = None
            try:
                with zipfile.ZipFile(strZipPath, "r") as zip_file:
                    zip_contents = zip_file.namelist()
                    test_logger.MSWriteLog("info", f"Received ZIP contents: {zip_contents}")
                    console_logger.debug(f"Received ZIP contents: {zip_contents}")
                    print(f"Received ZIP contents: {zip_contents}")
                    for filename in zip_contents:
                        if "DNameWMPR25001596979.xml" in filename and filename.endswith(".xml"):
                            xml_filename = filename
                        elif filename.startswith("Report") and filename.endswith(".csv"):
                            csv_filename = filename
                    if not xml_filename:
                        test_logger.MSWriteLog("error", f"No XML file found in ZIP containing DNameWMPR25001596979.xml")
                        self.fail(f"No XML file found in ZIP containing DNameWMPR25001596979.xml")
                    if not csv_filename:
                        test_logger.MSWriteLog("error", f"No CSV file found in ZIP starting with 'Report'")
                        self.fail(f"No CSV file found in ZIP starting with 'Report'")
                    test_logger.MSWriteLog("info", f"Found XML file in ZIP: {xml_filename}")
                    test_logger.MSWriteLog("info", f"Found CSV file in ZIP: {csv_filename}")
                    console_logger.debug(f"Found XML file in ZIP: {xml_filename}")
                    console_logger.debug(f"Found CSV file in ZIP: {csv_filename}")
                    print(f"Found XML file in ZIP: {xml_filename}")
                    print(f"Found CSV file in ZIP: {csv_filename}")
                    zip_file.extract(xml_filename, os.path.dirname(strZipPath))
                    zip_file.extract(csv_filename, os.path.dirname(strZipPath))
            except zipfile.BadZipFile as e:
                test_logger.MSWriteLog("error", f"Invalid ZIP file: {str(e)}")
                self.fail(f"Invalid ZIP file: {str(e)}")
            except Exception as e:
                test_logger.MSWriteLog("error", f"Error extracting files from ZIP: {str(e)}")
                self.fail(f"Error extracting files from ZIP: {str(e)}")

            strXmlPath = os.path.join(os.path.dirname(strZipPath), xml_filename)
            strCsvPath = os.path.join(os.path.dirname(strZipPath), csv_filename)
            self.assertTrue(os.path.exists(strXmlPath), f"XML file not found at: {strXmlPath}")
            self.assertTrue(os.path.exists(strCsvPath), f"CSV file not found at: {strCsvPath}")
            test_logger.MSWriteLog("info", f"XML extracted to: {strXmlPath}")
            test_logger.MSWriteLog("info", f"CSV extracted to: {strCsvPath}")
            console_logger.debug(f"XML extracted to: {strXmlPath}")
            console_logger.debug(f"CSV extracted to: {strCsvPath}")
            print(f"XML extracted to: {strXmlPath}")
            print(f"CSV extracted to: {strCsvPath}")

            # Compare XML with input XML, ignoring VOUCHERNUMBER tag
            test_logger.MSWriteLog("info", f"Comparing generated XML with input: {strInputXmlPath}, ignoring tags: ['VOUCHERNUMBER']")
            console_logger.debug(f"Comparing XML: {strInputXmlPath} vs {strXmlPath}, ignoring tags: ['VOUCHERNUMBER']")
            print(f"Comparing XML: {strInputXmlPath} vs {strXmlPath}, ignoring tags: ['VOUCHERNUMBER']")
            bAreSameXml, strLogMessageXml = compare_xml(
                self.logger,
                file1=strInputXmlPath,
                file2=strXmlPath,
                ignore_tags=["VOUCHERNUMBER"]
            )
            test_logger.MSWriteLog("info", f"XML comparison message: {strLogMessageXml}")
            console_logger.debug(f"XML comparison result: {strLogMessageXml}")
            print(f"XML comparison result: {strLogMessageXml}")
            if not bAreSameXml:
                test_logger.MSWriteLog("error", f"XML content mismatch between {strInputXmlPath} and {strXmlPath}.")
                self.fail("XML content mismatch.")
            else:
                test_logger.MSWriteLog("info", "XML content matched with input file after ignoring VOUCHERNUMBER tag.")

            # Compare CSV with input CSV, ignoring Received Date column
            test_logger.MSWriteLog("info", f"Comparing generated CSV with input: {strInputCsvPath}, ignoring columns: ['Received Date']")
            console_logger.debug(f"Comparing CSV: {strInputCsvPath} vs {strCsvPath}, ignoring columns: ['Received Date']")
            print(f"Comparing CSV: {strInputCsvPath} vs {strCsvPath}, ignoring columns: ['Received Date']")
            bAreSameCsv, strLogMessageCsv = compare_csv(
                self.logger,
                file1=strInputCsvPath,
                file2=strCsvPath,
                ignore_columns=["Received Date"]
            )
            test_logger.MSWriteLog("info", f"CSV comparison message: {strLogMessageCsv}")
            console_logger.debug(f"CSV comparison result: {strLogMessageCsv}")
            print(f"CSV comparison result: {strLogMessageCsv}")
            if not bAreSameCsv:
                test_logger.MSWriteLog("error", f"CSV content mismatch between {strInputCsvPath} and {strCsvPath} after ignoring Received Date column.")
                self.fail("CSV content mismatch.")
            else:
                test_logger.MSWriteLog("info", "CSV content matched with input file after ignoring Received Date column.")

        except Exception as e:
            test_logger.MSWriteLog("error", f"Unexpected error: {str(e)}")
            test_logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            console_logger.error(f"Test failed: {str(e)}")
            print(f"Test failed: {str(e)}")
            raise e

if __name__ == "__main__":
    try:
        console_logger.debug("Starting unittest execution")
        print("Starting unittest execution")
        asyncio.run(unittest.main(verbosity=2))
        console_logger.debug("Unittest execution completed")
        print("Unittest execution completed")
    except Exception as e:
        console_logger.error(f"Top-level error in test execution: {str(e)}")
        console_logger.error(f"Traceback: {traceback.format_exc()}")
        print(f"Top-level error: {str(e)}")
        raise e