# Quick Start Guide

## 🚀 Getting Started with the New Test Framework

This guide will help you quickly set up and start using the new modular test framework.

### 1. Setup Framework (First Time Only)

```bash
# Run the setup script to check everything is ready
python Test/setup_framework.py

# If you want to create a sample test file
python Test/setup_framework.py --create-sample
```

### 2. Check Current Configuration

```bash
# View current test configuration
python Test/config_manager.py --show

# Validate configuration
python Test/config_manager.py --validate
```

### 3. Run Tests

```bash
# Run all tests
python Test/test_runner.py

# Run all tests in learning mode (creates new reference data)
python Test/test_runner.py --brelearn

# Run specific test module
python Test/test_runner.py --module test_IndianInvRouteParagGeberit

# Show available tests
python Test/test_runner.py --summary
```

### 4. Key Benefits You Get Immediately

✅ **Automatic Directory Naming**: No more hardcoded paths!
- Input: `Test/Input/{your_test_module_name}/`
- Reference: `Test/Reference/{your_test_module_name}/`
- Results: `Test/Results/{your_test_module_name}/`

✅ **Unique Result Storage**: Never overwrite previous results!
- Each test run creates: `Test/Results/{module}/{method}_{file}_{timestamp}/`

✅ **Centralized Configuration**: Update all tests from one place!
- Edit `Test/test_config.py` to change API URLs, timeouts, etc.

✅ **Single Test Runner**: Run all tests from one command!
- `python Test/test_runner.py` runs everything

## 🔧 Quick Configuration Updates

### Change API Settings

```python
# In Test/test_config.py, update:
API_URL = "http://your-new-server:8034/IndianInvTally/process_doc"
API_TIMEOUT = 1800  # 30 minutes
BRELEARN = True     # Enable learning mode
```

### Add New Test Configuration

```python
# In Test/test_config.py, add to TEST_DATA_CONFIGS:
'test_YourNewModule': {
    'test_YourMethod': {
        'doc_name': 'YourDocName',
        'pdf_filename': 'your_file.pdf',
        'xml_extract_pattern': 'DName2512002397.xml',
        'csv_extract_pattern': 'Report'
    }
}
```

## 📝 Create a New Test (5 Minutes)

### Step 1: Create Test File

Create `Test/test_YourNewTest.py`:

```python
from Test.base_test import BaseIndianInvTest

class TestYourNewTest(BaseIndianInvTest):
    async def test_your_method(self):
        # Get configuration (automatically loaded)
        doc_name = self.test_data_config['doc_name']
        pdf_filename = self.test_data_config['pdf_filename']
        
        # Verify license
        license_data = self.verify_license()
        
        # Get file paths (automatic directory naming)
        file_paths = self.get_file_paths(doc_name, pdf_filename)
        
        # Verify input files
        self.verify_input_files(file_paths)
        
        # Check server
        if not self.is_server_listening():
            self.fail("Server not available")
        
        # Send API request
        response = await self.send_api_request(file_paths, pdf_filename, license_data)
        
        # Validate and save response
        self.validate_and_save_response(response, file_paths['zip_file'])
        
        # Extract files
        xml_path, csv_path = self.extract_and_rename_files(
            file_paths['zip_file'], file_paths['output_dir'], doc_name,
            self.test_data_config['xml_extract_pattern'],
            self.test_data_config['csv_extract_pattern']
        )
        
        # Compare files
        self.compare_files(file_paths, xml_path, csv_path)
```

### Step 2: Add Configuration

In `Test/test_config.py`, add:

```python
'test_YourNewTest': {
    'test_your_method': {
        'doc_name': 'YourDocName',
        'pdf_filename': 'your_file.pdf',
        'xml_extract_pattern': 'DName2512002397.xml',
        'csv_extract_pattern': 'Report'
    }
}
```

### Step 3: Run Your Test

```bash
python Test/test_runner.py --module test_YourNewTest
```

## 🔄 Migrate Existing Tests

If you have existing tests to migrate:

```bash
# Analyze existing tests
python Test/migrate_tests.py --analyze-only

# Migrate all tests (creates *_migrated.py files)
python Test/migrate_tests.py

# Migrate and update configuration
python Test/migrate_tests.py --update-config
```

## 📊 Directory Structure After Setup

```
Test/
├── Input/                          # Your test input files
│   └── test_YourModule/           # Auto-named by module
│       └── test_method/           # Auto-named by test method
│           └── your_file.pdf
├── Reference/                      # Reference files for comparison
│   └── test_YourModule/
│       └── test_method/
│           ├── YourDoc.xml
│           └── Report_YourDoc.csv
├── Results/                        # Unique timestamped results
│   └── test_YourModule/
│       ├── test_method_file_20250120_143022/
│       └── test_method_file_20250120_143155/
└── Logs/                          # Per-module log files
    ├── test_runner.log
    └── test_YourModule.log
```

## 🎯 Common Use Cases

### Learning Mode (Create New Reference Data)
```bash
python Test/config_manager.py --brelearn true
python Test/test_runner.py --module test_YourModule
```

### Comparison Mode (Test Against Reference)
```bash
python Test/config_manager.py --brelearn false
python Test/test_runner.py --module test_YourModule
```

### Change API Server
```bash
python Test/config_manager.py --api-url "http://new-server:8034/IndianInvTally/process_doc"
```

### Run Specific Test
```bash
python Test/test_runner.py --module test_IndianInvRouteParagGeberit --method test_ParagGeberit
```

## 🆘 Troubleshooting

### "No test data configuration found"
- Add your test to `TEST_DATA_CONFIGS` in `Test/test_config.py`

### "License file not found"
- Check `LICENSE_FILE` path in `Test/test_config.py`
- Ensure license file exists

### "Server not available"
- Check `API_URL` in configuration
- Verify server is running

### "Input file not found"
- Ensure files are in correct input directory: `Test/Input/{module_name}/{test_method}/`

## 📚 Need More Help?

- **Full Documentation**: `Test/README.md`
- **Configuration Help**: `python Test/config_manager.py --show`
- **Test Summary**: `python Test/test_runner.py --summary`
- **Validate Setup**: `python Test/setup_framework.py`

## 🎉 You're Ready!

The framework is designed to make testing easier and more organized. Start with the existing examples and gradually migrate your tests. Each test automatically gets its own directories and unique result storage!
