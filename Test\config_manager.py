"""
Configuration Manager Module

This module provides utilities for managing test configurations,
allowing easy updates to test parameters without modifying individual test files.
"""

import json
import os
from typing import Dict, Any, List
from Test.test_config import TestConfig, TEST_DATA_CONFIGS


class ConfigManager:
    """
    Configuration manager for test framework.
    
    Provides methods to:
    - Update global configuration
    - Manage test data configurations
    - Export/import configurations
    - Validate configurations
    """
    
    def __init__(self):
        self.config_file = os.path.join(TestConfig.TEST_DIR, "runtime_config.json")
    
    def update_global_config(self, **kwargs):
        """
        Update global configuration parameters.
        
        Args:
            **kwargs: Configuration parameters to update
        """
        updated_params = []
        
        for key, value in kwargs.items():
            if hasattr(TestConfig, key):
                old_value = getattr(TestConfig, key)
                setattr(TestConfig, key, value)
                updated_params.append(f"{key}: {old_value} -> {value}")
            else:
                print(f"Warning: Unknown configuration parameter: {key}")
        
        if updated_params:
            print("Updated configuration parameters:")
            for param in updated_params:
                print(f"  - {param}")
    
    def update_test_data_config(self, module_name: str, test_method: str, **kwargs):
        """
        Update test data configuration for a specific test.
        
        Args:
            module_name: Name of the test module
            test_method: Name of the test method
            **kwargs: Test data parameters to update
        """
        if module_name not in TEST_DATA_CONFIGS:
            TEST_DATA_CONFIGS[module_name] = {}
        
        if test_method not in TEST_DATA_CONFIGS[module_name]:
            TEST_DATA_CONFIGS[module_name][test_method] = {}
        
        TEST_DATA_CONFIGS[module_name][test_method].update(kwargs)
        
        print(f"Updated test data config for {module_name}.{test_method}:")
        for key, value in kwargs.items():
            print(f"  - {key}: {value}")
    
    def get_current_config(self) -> Dict[str, Any]:
        """
        Get current configuration as a dictionary.
        
        Returns:
            Dict containing current configuration
        """
        config = {
            'global_config': {
                'BRELEARN': TestConfig.BRELEARN,
                'API_URL': TestConfig.API_URL,
                'API_HOST': TestConfig.API_HOST,
                'API_PORT': TestConfig.API_PORT,
                'API_TIMEOUT': TestConfig.API_TIMEOUT,
                'LICENSE_FILE': TestConfig.LICENSE_FILE,
                'XML_IGNORE_TAGS': TestConfig.XML_IGNORE_TAGS,
                'CSV_IGNORE_COLUMNS': TestConfig.CSV_IGNORE_COLUMNS
            },
            'test_data_configs': TEST_DATA_CONFIGS,
            'api_config': TestConfig.API_CONFIG
        }
        return config
    
    def save_config(self, filename: str = None):
        """
        Save current configuration to a JSON file.
        
        Args:
            filename: Optional filename. Uses default if not provided.
        """
        filename = filename or self.config_file
        config = self.get_current_config()
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Configuration saved to: {filename}")
    
    def load_config(self, filename: str = None):
        """
        Load configuration from a JSON file.
        
        Args:
            filename: Optional filename. Uses default if not provided.
        """
        filename = filename or self.config_file
        
        if not os.path.exists(filename):
            print(f"Configuration file not found: {filename}")
            return
        
        with open(filename, 'r') as f:
            config = json.load(f)
        
        # Update global config
        if 'global_config' in config:
            self.update_global_config(**config['global_config'])
        
        # Update test data configs
        if 'test_data_configs' in config:
            TEST_DATA_CONFIGS.update(config['test_data_configs'])
        
        # Update API config
        if 'api_config' in config:
            TestConfig.API_CONFIG.update(config['api_config'])
        
        print(f"Configuration loaded from: {filename}")
    
    def print_current_config(self):
        """Print current configuration in a readable format."""
        print("\n" + "="*80)
        print("CURRENT TEST CONFIGURATION")
        print("="*80)
        
        # Global configuration
        print("\nGlobal Configuration:")
        print("-" * 40)
        print(f"BRELEARN Mode: {TestConfig.BRELEARN}")
        print(f"API URL: {TestConfig.API_URL}")
        print(f"API Host: {TestConfig.API_HOST}")
        print(f"API Port: {TestConfig.API_PORT}")
        print(f"API Timeout: {TestConfig.API_TIMEOUT}")
        print(f"License File: {TestConfig.LICENSE_FILE}")
        print(f"XML Ignore Tags: {TestConfig.XML_IGNORE_TAGS}")
        print(f"CSV Ignore Columns: {TestConfig.CSV_IGNORE_COLUMNS}")
        
        # Test data configurations
        print("\nTest Data Configurations:")
        print("-" * 40)
        for module_name, module_config in TEST_DATA_CONFIGS.items():
            print(f"\nModule: {module_name}")
            for test_method, test_config in module_config.items():
                print(f"  Test: {test_method}")
                for key, value in test_config.items():
                    print(f"    {key}: {value}")
        
        print("\n" + "="*80)
    
    def create_test_template(self, module_name: str, test_method: str, 
                           doc_name: str, pdf_filename: str) -> Dict[str, str]:
        """
        Create a template configuration for a new test.
        
        Args:
            module_name: Name of the test module
            test_method: Name of the test method
            doc_name: Document name for output files
            pdf_filename: Input PDF filename
            
        Returns:
            Dict containing template configuration
        """
        template = {
            'doc_name': doc_name,
            'pdf_filename': pdf_filename,
            'xml_extract_pattern': 'DName2512002397.xml',  # Default pattern
            'csv_extract_pattern': 'Report'  # Default pattern
        }
        
        # Add to TEST_DATA_CONFIGS
        if module_name not in TEST_DATA_CONFIGS:
            TEST_DATA_CONFIGS[module_name] = {}
        
        TEST_DATA_CONFIGS[module_name][test_method] = template
        
        print(f"Created template configuration for {module_name}.{test_method}:")
        for key, value in template.items():
            print(f"  - {key}: {value}")
        
        return template
    
    def validate_config(self) -> List[str]:
        """
        Validate current configuration and return list of issues.
        
        Returns:
            List of validation issues (empty if no issues)
        """
        issues = []
        
        # Check if license file exists
        if not os.path.exists(TestConfig.LICENSE_FILE):
            issues.append(f"License file not found: {TestConfig.LICENSE_FILE}")
        
        # Check if required directories exist
        required_dirs = [TestConfig.BASE_INPUT_DIR, TestConfig.BASE_REFERENCE_DIR, TestConfig.LOG_DIR]
        for directory in required_dirs:
            if not os.path.exists(directory):
                issues.append(f"Required directory not found: {directory}")
        
        # Validate test data configurations
        for module_name, module_config in TEST_DATA_CONFIGS.items():
            for test_method, test_config in module_config.items():
                required_keys = ['doc_name', 'pdf_filename', 'xml_extract_pattern', 'csv_extract_pattern']
                for key in required_keys:
                    if key not in test_config:
                        issues.append(f"Missing required key '{key}' in {module_name}.{test_method}")
        
        return issues
    
    def setup_environment(self):
        """
        Set up the test environment by creating necessary directories.
        """
        directories = [
            TestConfig.BASE_INPUT_DIR,
            TestConfig.BASE_REFERENCE_DIR,
            TestConfig.BASE_RESULTS_DIR,
            TestConfig.LOG_DIR
        ]
        
        created_dirs = []
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                created_dirs.append(directory)
        
        if created_dirs:
            print("Created directories:")
            for directory in created_dirs:
                print(f"  - {directory}")
        else:
            print("All required directories already exist.")


def main():
    """Main function for command-line configuration management."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Configuration Manager for Test Framework")
    
    parser.add_argument('--show', '-s', action='store_true', help='Show current configuration')
    parser.add_argument('--save', action='store_true', help='Save current configuration')
    parser.add_argument('--load', type=str, help='Load configuration from file')
    parser.add_argument('--validate', '-v', action='store_true', help='Validate current configuration')
    parser.add_argument('--setup', action='store_true', help='Setup test environment')
    parser.add_argument('--brelearn', type=bool, help='Set BRELEARN mode')
    parser.add_argument('--api-url', type=str, help='Set API URL')
    parser.add_argument('--api-timeout', type=int, help='Set API timeout')
    
    args = parser.parse_args()
    
    manager = ConfigManager()
    
    # Handle configuration updates
    updates = {}
    if args.brelearn is not None:
        updates['BRELEARN'] = args.brelearn
    if args.api_url:
        updates['API_URL'] = args.api_url
    if args.api_timeout:
        updates['API_TIMEOUT'] = args.api_timeout
    
    if updates:
        manager.update_global_config(**updates)
    
    # Handle other operations
    if args.show:
        manager.print_current_config()
    elif args.save:
        manager.save_config()
    elif args.load:
        manager.load_config(args.load)
    elif args.validate:
        issues = manager.validate_config()
        if issues:
            print("Configuration issues found:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("Configuration is valid.")
    elif args.setup:
        manager.setup_environment()
    else:
        manager.print_current_config()


if __name__ == "__main__":
    main()
