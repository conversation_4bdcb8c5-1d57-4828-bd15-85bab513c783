# Test Framework Documentation

## Overview

This is a modular, well-organized test framework for Indian Invoice Route API testing. The framework provides:

- **Automatic directory naming** based on test module names
- **Unique result storage** to avoid overwriting previous test results
- **Centralized configuration management** for easy parameter updates
- **Single test runner** to execute all tests from one place
- **Modular structure** following best practices
- **Comprehensive logging** with per-module log files

## Directory Structure

```
Test/
├── README.md                           # This documentation
├── test_config.py                      # Global configuration management
├── base_test.py                        # Base test class with common functionality
├── test_runner.py                      # Centralized test runner
├── config_manager.py                   # Configuration management utilities
├── CustomHelper.py                     # Existing helper utilities
├── 
├── Input/                              # Input test data (auto-organized by module)
│   ├── test_IndianInvRouteParagGeberit/
│   ├── test_IndianInvRouteParagToto/
│   └── ...
├── 
├── Reference/                          # Reference data for comparison
│   ├── test_IndianInvRouteParagGeberit/
│   ├── test_IndianInvRouteParagToto/
│   └── ...
├── 
├── Results/                            # Unique test results (timestamped)
│   ├── test_IndianInvRouteParagGeberit/
│   │   ├── test_ParagGeberit_GEBERIT_pdf_20250120_143022/
│   │   ├── test_ParagGeberit_GEBERIT_pdf_20250120_143155/
│   │   └── ...
│   └── ...
├── 
├── Logs/                               # Log files
│   ├── test_runner.log
│   ├── test_IndianInvRouteParagGeberit.log
│   └── ...
└── 
└── test_*.py                           # Individual test modules
```

## Key Features

### 1. Automatic Directory Naming

The framework automatically creates directories based on the test module name:

- **Input Directory**: `Test/Input/{module_name}/`
- **Reference Directory**: `Test/Reference/{module_name}/`
- **Results Directory**: `Test/Results/{module_name}/`
- **Log File**: `Test/Logs/{module_name}.log`

For example, `test_IndianInvRouteParagGeberit.py` automatically uses:
- Input: `Test/Input/test_IndianInvRouteParagGeberit/`
- Reference: `Test/Reference/test_IndianInvRouteParagGeberit/`
- Results: `Test/Results/test_IndianInvRouteParagGeberit/`
- Log: `Test/Logs/test_IndianInvRouteParagGeberit.log`

### 2. Unique Result Storage

Each test run creates a unique result directory with timestamp and file identifier:
```
Test/Results/{module_name}/{test_method}_{file_identifier}_{timestamp}/
```

This ensures that:
- Previous test results are never overwritten
- You can compare results from different test runs
- Each file tested gets its own result directory

### 3. Centralized Configuration

All test parameters are managed in `test_config.py`:

```python
# Global settings
BRELEARN = False                    # Learning mode vs comparison mode
API_URL = "http://..."             # API endpoint
API_TIMEOUT = 1200                 # Request timeout
LICENSE_FILE = "Test/license.lic"  # License file path

# Test-specific configurations
TEST_DATA_CONFIGS = {
    'test_IndianInvRouteParagGeberit': {
        'test_ParagGeberit': {
            'doc_name': 'ParagGEBERIT',
            'pdf_filename': 'GEBERIT.pdf',
            'xml_extract_pattern': 'DName2512002397.xml',
            'csv_extract_pattern': 'Report'
        }
    }
}
```

## Usage

### Running All Tests

```bash
# Run all tests
python Test/test_runner.py

# Run all tests in learning mode
python Test/test_runner.py --brelearn

# Run with different verbosity
python Test/test_runner.py --verbosity 1
```

### Running Specific Tests

```bash
# Run specific module
python Test/test_runner.py --module test_IndianInvRouteParagGeberit

# Run specific test class
python Test/test_runner.py --module test_IndianInvRouteParagGeberit --class TestIndianInvRouteParagGeberit

# Run specific test method
python Test/test_runner.py --module test_IndianInvRouteParagGeberit --class TestIndianInvRouteParagGeberit --method test_ParagGeberit
```

### Configuration Management

```bash
# Show current configuration
python Test/config_manager.py --show

# Save current configuration
python Test/config_manager.py --save

# Load configuration from file
python Test/config_manager.py --load runtime_config.json

# Validate configuration
python Test/config_manager.py --validate

# Setup test environment
python Test/config_manager.py --setup

# Update specific settings
python Test/config_manager.py --brelearn true --api-timeout 1800
```

### Creating New Tests

1. **Create test module** (e.g., `test_NewModule.py`):

```python
from Test.base_test import BaseIndianInvTest

class TestNewModule(BaseIndianInvTest):
    async def test_NewTest(self):
        # Get test configuration
        doc_name = self.test_data_config['doc_name']
        pdf_filename = self.test_data_config['pdf_filename']
        
        # Verify license
        license_data = self.verify_license()
        
        # Get file paths (automatically uses module name)
        file_paths = self.get_file_paths(doc_name, pdf_filename)
        
        # Verify input files
        self.verify_input_files(file_paths)
        
        # Check server
        if not self.is_server_listening():
            self.fail("Server not available")
        
        # Send API request
        response = await self.send_api_request(file_paths, pdf_filename, license_data)
        
        # Validate and save response
        self.validate_and_save_response(response, file_paths['zip_file'])
        
        # Extract and rename files
        xml_path, csv_path = self.extract_and_rename_files(
            file_paths['zip_file'],
            file_paths['output_dir'],
            doc_name,
            self.test_data_config['xml_extract_pattern'],
            self.test_data_config['csv_extract_pattern']
        )
        
        # Compare files
        self.compare_files(file_paths, xml_path, csv_path)
```

2. **Add test configuration** to `test_config.py`:

```python
TEST_DATA_CONFIGS = {
    'test_NewModule': {
        'test_NewTest': {
            'doc_name': 'NewDoc',
            'pdf_filename': 'new_file.pdf',
            'xml_extract_pattern': 'DName2512002397.xml',
            'csv_extract_pattern': 'Report'
        }
    }
}
```

## Configuration Options

### Global Configuration (TestConfig class)

- `BRELEARN`: Boolean - Enable learning mode (True) or comparison mode (False)
- `API_URL`: String - API endpoint URL
- `API_HOST`: String - API server host
- `API_PORT`: Integer - API server port
- `API_TIMEOUT`: Integer - Request timeout in seconds
- `LICENSE_FILE`: String - Path to license file
- `XML_IGNORE_TAGS`: List - XML tags to ignore during comparison
- `CSV_IGNORE_COLUMNS`: List - CSV columns to ignore during comparison

### Test Data Configuration

Each test can have its own configuration:

- `doc_name`: Document name for output files
- `pdf_filename`: Input PDF filename
- `xml_extract_pattern`: Pattern to find XML file in ZIP
- `csv_extract_pattern`: Pattern to find CSV file in ZIP

## Best Practices

1. **Use descriptive test module names** that reflect the functionality being tested
2. **Keep test data configurations updated** in `test_config.py`
3. **Use BRELEARN mode** when creating new reference data
4. **Review unique result directories** to track test history
5. **Validate configuration** before running tests
6. **Use the test runner** instead of running individual test files

## Troubleshooting

### Common Issues

1. **"No test data configuration found"**
   - Add your test configuration to `TEST_DATA_CONFIGS` in `test_config.py`

2. **"License file not found"**
   - Check the `LICENSE_FILE` path in configuration
   - Ensure the license file exists

3. **"Server not available"**
   - Verify the API server is running
   - Check `API_URL`, `API_HOST`, and `API_PORT` configuration

4. **"Input file not found"**
   - Ensure test files are in the correct input directory
   - Check file naming matches configuration

### Debugging

1. **Check logs** in `Test/Logs/` directory
2. **Use verbose mode** (`--verbosity 2`)
3. **Validate configuration** (`python Test/config_manager.py --validate`)
4. **Run specific tests** to isolate issues

## Migration from Old Framework

To migrate existing tests:

1. **Inherit from BaseIndianInvTest** instead of unittest.TestCase
2. **Add test configuration** to `TEST_DATA_CONFIGS`
3. **Remove duplicate code** (logging, file paths, API requests)
4. **Use framework methods** (verify_license, get_file_paths, etc.)
5. **Update file paths** to use automatic directory naming

The new framework maintains compatibility with existing test data and directory structures.
