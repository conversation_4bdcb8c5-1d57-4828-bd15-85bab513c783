"""
Test Runner Module

This module provides a centralized test runner that can execute all tests
from a single file. It allows for easy configuration updates and batch execution.
"""

import unittest
import asyncio
import os
import sys
import importlib
import glob
from typing import List, Dict, Any, Optional
from datetime import datetime
import argparse

# Add the Test directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_config import TestConfig, TEST_DATA_CONFIGS
from CustomHelper import TestLogger


class TestRunner:
    """
    Centralized test runner for all Indian Invoice Route tests.
    """
    
    def __init__(self):
        self.test_modules = []
        self.results = {}
        self.logger = None
        self._initialize_logger()
    
    def _initialize_logger(self):
        """Initialize the test runner logger."""
        os.makedirs(TestConfig.LOG_DIR, exist_ok=True)
        self.logger = TestLogger(TestConfig.LOG_DIR, "test_runner.log")
        self.logger.MSWriteLog("info", "Test Runner initialized")
    
    def discover_test_modules(self, pattern: str = "test_*.py") -> List[str]:
        """
        Discover all test modules in the Test directory.
        
        Args:
            pattern: File pattern to match test files
            
        Returns:
            List of test module names
        """
        test_files = glob.glob(os.path.join(TestConfig.TEST_DIR, pattern))
        test_modules = []
        
        for test_file in test_files:
            module_name = os.path.splitext(os.path.basename(test_file))[0]
            # Skip the runner itself and config files
            if module_name not in ['test_runner', 'test_config', 'base_test']:
                test_modules.append(module_name)
        
        self.logger.MSWriteLog("info", f"Discovered test modules: {test_modules}")
        return test_modules
    
    def load_test_module(self, module_name: str):
        """
        Load a test module dynamically.
        
        Args:
            module_name: Name of the test module to load
            
        Returns:
            Loaded module object
        """
        try:
            # Import the module
            module = importlib.import_module(module_name)
            self.logger.MSWriteLog("info", f"Loaded test module: {module_name}")
            return module
        except ImportError as e:
            self.logger.MSWriteLog("error", f"Failed to load module {module_name}: {str(e)}")
            return None
    
    def create_test_suite(self, test_modules: List[str] = None) -> unittest.TestSuite:
        """
        Create a test suite from specified modules or all discovered modules.
        
        Args:
            test_modules: List of test module names. If None, discovers all modules.
            
        Returns:
            unittest.TestSuite containing all tests
        """
        if test_modules is None:
            test_modules = self.discover_test_modules()
        
        suite = unittest.TestSuite()
        
        for module_name in test_modules:
            module = self.load_test_module(module_name)
            if module:
                # Get all test classes from the module
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (isinstance(attr, type) and 
                        issubclass(attr, unittest.TestCase) and 
                        attr != unittest.TestCase):
                        
                        # Add all test methods from the class
                        test_loader = unittest.TestLoader()
                        class_suite = test_loader.loadTestsFromTestCase(attr)
                        suite.addTest(class_suite)
                        
                        self.logger.MSWriteLog("info", f"Added test class {attr_name} from {module_name}")
        
        return suite
    
    def run_tests(self, test_modules: List[str] = None, verbosity: int = 2) -> unittest.TestResult:
        """
        Run tests and return results.
        
        Args:
            test_modules: List of test module names to run. If None, runs all.
            verbosity: Test runner verbosity level
            
        Returns:
            unittest.TestResult containing test results
        """
        self.logger.MSWriteLog("info", "Starting test execution")
        
        # Create test suite
        suite = self.create_test_suite(test_modules)
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=verbosity, stream=sys.stdout)
        
        try:
            result = asyncio.run(self._run_async_tests(suite, runner))
            self.logger.MSWriteLog("info", f"Test execution completed. Ran {result.testsRun} tests")
            
            if result.failures:
                self.logger.MSWriteLog("error", f"Failures: {len(result.failures)}")
                for test, traceback in result.failures:
                    self.logger.MSWriteLog("error", f"FAIL: {test} - {traceback}")
            
            if result.errors:
                self.logger.MSWriteLog("error", f"Errors: {len(result.errors)}")
                for test, traceback in result.errors:
                    self.logger.MSWriteLog("error", f"ERROR: {test} - {traceback}")
            
            return result
            
        except Exception as e:
            self.logger.MSWriteLog("error", f"Test execution failed: {str(e)}")
            raise
    
    async def _run_async_tests(self, suite: unittest.TestSuite, runner: unittest.TextTestRunner):
        """
        Run async tests using asyncio.
        
        Args:
            suite: Test suite to run
            runner: Test runner instance
            
        Returns:
            Test results
        """
        return runner.run(suite)
    
    def run_specific_test(self, module_name: str, test_class_name: str = None, 
                         test_method_name: str = None, verbosity: int = 2):
        """
        Run a specific test module, class, or method.
        
        Args:
            module_name: Name of the test module
            test_class_name: Optional test class name
            test_method_name: Optional test method name
            verbosity: Test runner verbosity level
        """
        self.logger.MSWriteLog("info", f"Running specific test: {module_name}.{test_class_name}.{test_method_name}")
        
        module = self.load_test_module(module_name)
        if not module:
            return
        
        suite = unittest.TestSuite()
        
        if test_class_name and test_method_name:
            # Run specific test method
            test_class = getattr(module, test_class_name)
            suite.addTest(test_class(test_method_name))
        elif test_class_name:
            # Run all tests in specific class
            test_class = getattr(module, test_class_name)
            test_loader = unittest.TestLoader()
            suite = test_loader.loadTestsFromTestCase(test_class)
        else:
            # Run all tests in module
            test_loader = unittest.TestLoader()
            suite = test_loader.loadTestsFromModule(module)
        
        # Run the tests
        runner = unittest.TextTestRunner(verbosity=verbosity, stream=sys.stdout)
        asyncio.run(self._run_async_tests(suite, runner))
    
    def update_global_config(self, **kwargs):
        """
        Update global configuration parameters.
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(TestConfig, key):
                setattr(TestConfig, key, value)
                self.logger.MSWriteLog("info", f"Updated config: {key} = {value}")
            else:
                self.logger.MSWriteLog("warning", f"Unknown config parameter: {key}")
    
    def print_test_summary(self):
        """Print a summary of available tests."""
        print("\n" + "="*80)
        print("AVAILABLE TESTS SUMMARY")
        print("="*80)
        
        test_modules = self.discover_test_modules()
        
        for module_name in test_modules:
            print(f"\nModule: {module_name}")
            print("-" * 40)
            
            # Get test data config if available
            if module_name in TEST_DATA_CONFIGS:
                config = TEST_DATA_CONFIGS[module_name]
                for test_method, test_config in config.items():
                    print(f"  Test: {test_method}")
                    print(f"    PDF File: {test_config.get('pdf_filename', 'N/A')}")
                    print(f"    Doc Name: {test_config.get('doc_name', 'N/A')}")
            else:
                print("  No configuration found")
        
        print("\n" + "="*80)


def main():
    """Main function for command-line execution."""
    parser = argparse.ArgumentParser(description="Test Runner for Indian Invoice Route Tests")
    
    parser.add_argument('--module', '-m', type=str, help='Specific module to run')
    parser.add_argument('--class', '-c', type=str, dest='test_class', help='Specific test class to run')
    parser.add_argument('--method', '-t', type=str, dest='test_method', help='Specific test method to run')
    parser.add_argument('--verbosity', '-v', type=int, default=2, help='Test verbosity level (0-2)')
    parser.add_argument('--brelearn', '-b', action='store_true', help='Enable BRELEARN mode')
    parser.add_argument('--summary', '-s', action='store_true', help='Show test summary')
    parser.add_argument('--list', '-l', action='store_true', help='List available test modules')
    
    args = parser.parse_args()
    
    # Create test runner
    runner = TestRunner()
    
    # Update configuration if needed
    if args.brelearn:
        runner.update_global_config(BRELEARN=True)
    
    # Handle different execution modes
    if args.summary:
        runner.print_test_summary()
    elif args.list:
        modules = runner.discover_test_modules()
        print("Available test modules:")
        for module in modules:
            print(f"  - {module}")
    elif args.module:
        runner.run_specific_test(
            args.module, 
            args.test_class, 
            args.test_method, 
            args.verbosity
        )
    else:
        # Run all tests
        runner.run_tests(verbosity=args.verbosity)


if __name__ == "__main__":
    main()
