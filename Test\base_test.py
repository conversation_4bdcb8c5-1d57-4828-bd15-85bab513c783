"""
Base Test Class Module

This module provides a base test class that contains common functionality
for all test cases, reducing code duplication and ensuring consistency.
"""

import unittest
import os
import asyncio
import socket
import json
import httpx
from typing import Dict, Any, Optional, Tuple
from Test.CustomHelper import Test<PERSON><PERSON><PERSON>, CLicenseHelper, calculate_checksum, compare_xml, compare_csv, extract_zip
from Test.test_config import TestConfig, TestCaseConfig, get_test_data_config


class BaseIndianInvTest(unittest.IsolatedAsyncioTestCase):
    """
    Base test class for Indian Invoice Route tests.
    
    This class provides common functionality for all test cases including:
    - Automatic directory setup based on test module name
    - Logging configuration
    - Server connectivity checks
    - API request handling
    - File comparison utilities
    - Unique result storage
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.test_config = None
        self.logger = None
        self.test_module_name = None
        self.test_data_config = None
        
    async def asyncSetUp(self):
        """
        Set up the test case by creating directories and initializing logging.
        """
        try:
            # Initialize test configuration
            self.test_module_name = TestConfig.get_test_module_name()
            self.test_config = TestCaseConfig(self.test_module_name)
            
            # Get test-specific data configuration
            self.test_data_config = get_test_data_config(
                self.test_module_name, 
                self._testMethodName
            )
            
            # Create all necessary directories
            self._create_directories()
            
            # Initialize logger
            self._initialize_logger()
            
            # Set up test case specific paths
            self._setup_test_paths()
            
            self.logger.MSWriteLog("info", f"Setup completed for: {self._testMethodName}")
            
        except Exception as e:
            if self.logger:
                self.logger.MSWriteLog("error", f"Setup failed: {str(e)}")
            raise
    
    async def asyncTearDown(self):
        """
        Clean up after the test case.
        """
        try:
            if self.logger:
                self.logger.MSWriteLog("info", f"Teardown completed for: {self._testMethodName}")
        except Exception as e:
            if self.logger:
                self.logger.MSWriteLog("error", f"Teardown failed: {str(e)}")
            raise
    
    def _create_directories(self):
        """Create all necessary directories for the test."""
        directories = [
            self.test_config.directories['input_dir'],
            self.test_config.directories['reference_dir'],
            self.test_config.directories['results_dir'],
            self.test_config.directories['log_dir']
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def _initialize_logger(self):
        """Initialize the test logger."""
        self.logger = TestLogger(
            self.test_config.directories['log_dir'],
            self.test_config.log_file
        )
        self.logger.MSWriteLog("info", f"Test logger initialized for {self.test_module_name}")
    
    def _setup_test_paths(self):
        """Set up test-specific file paths."""
        # Create test method specific directory in input
        self.strCurTestCaseDirPath = os.path.join(
            self.test_config.directories['input_dir'], 
            self._testMethodName
        )
        os.makedirs(self.strCurTestCaseDirPath, exist_ok=True)
        
        # Create corresponding reference directory
        self.reference_test_dir = os.path.join(
            self.test_config.directories['reference_dir'], 
            self._testMethodName
        )
        os.makedirs(self.reference_test_dir, exist_ok=True)
    
    def get_unique_result_directory(self, file_identifier: str = None) -> str:
        """
        Get a unique result directory for storing test results.
        
        Args:
            file_identifier: Optional identifier for the file being tested
            
        Returns:
            str: Path to unique result directory
        """
        result_dir = TestConfig.get_unique_result_dir(
            self.test_module_name,
            self._testMethodName,
            file_identifier
        )
        os.makedirs(result_dir, exist_ok=True)
        return result_dir
    
    def is_server_listening(self, host: str = None, port: int = None, timeout: int = 2) -> bool:
        """
        Check if the server is listening on the specified host and port.
        
        Args:
            host: Server host (defaults to config value)
            port: Server port (defaults to config value)
            timeout: Timeout for connection attempt
            
        Returns:
            bool: True if server is listening, False otherwise
        """
        host = host or TestConfig.API_HOST
        port = port or TestConfig.API_PORT
        
        try:
            with socket.create_connection((host, port), timeout=timeout):
                self.logger.MSWriteLog("info", f"Server at {host}:{port} is listening")
                return True
        except (socket.timeout, ConnectionRefusedError) as e:
            self.logger.MSWriteLog("error", f"Server at {host}:{port} is not listening: {str(e)}")
            return False
    
    def verify_license(self) -> Dict[str, Any]:
        """
        Verify the license file and return license data.
        
        Returns:
            Dict containing license data including token and user ID
        """
        if not os.path.exists(TestConfig.LICENSE_FILE):
            self.logger.MSWriteLog("error", f"License file not found: {TestConfig.LICENSE_FILE}")
            self.fail(f"License file not found: {TestConfig.LICENSE_FILE}")
        
        license_data = CLicenseHelper.MSVerifyLicense(TestConfig.LICENSE_FILE)
        strUserToken = license_data["Token"]
        user_id = license_data.get("uid", "unknown")
        
        self.logger.MSWriteLog("info", f"License verified, token: {strUserToken[:10]}..., uid: {user_id}")
        return license_data
    
    def get_file_paths(self, doc_name: str, pdf_filename: str) -> Dict[str, str]:
        """
        Get all file paths for a test case.
        
        Args:
            doc_name: Document name for output files
            pdf_filename: Input PDF filename
            
        Returns:
            Dict containing all file paths
        """
        # Determine output directory based on BRELEARN mode
        if TestConfig.BRELEARN:
            output_dir = self.strCurTestCaseDirPath
        else:
            output_dir = self.reference_test_dir
        
        return {
            'input_pdf': os.path.join(self.strCurTestCaseDirPath, pdf_filename),
            'input_xml': os.path.join(self.strCurTestCaseDirPath, f"{doc_name}.xml"),
            'input_csv': os.path.join(self.strCurTestCaseDirPath, f"Report_{doc_name}.csv"),
            'output_dir': output_dir,
            'zip_file': os.path.join(output_dir, "Content_zipFile.zip"),
            'output_xml': os.path.join(output_dir, f"{doc_name}.xml"),
            'output_csv': os.path.join(output_dir, f"Report_{doc_name}.csv")
        }
    
    def verify_input_files(self, file_paths: Dict[str, str]):
        """
        Verify that required input files exist.
        
        Args:
            file_paths: Dictionary containing file paths
        """
        # Always verify PDF file
        if not os.path.exists(file_paths['input_pdf']):
            self.logger.MSWriteLog("error", f"Input PDF file not found: {file_paths['input_pdf']}")
            self.fail(f"Input PDF file not found: {file_paths['input_pdf']}")
        
        # Verify XML and CSV files only if not in BRELEARN mode
        if not TestConfig.BRELEARN:
            for file_type, file_path in [('XML', file_paths['input_xml']), ('CSV', file_paths['input_csv'])]:
                if not os.path.exists(file_path):
                    self.logger.MSWriteLog("error", f"Input {file_type} file not found: {file_path}")
                    self.fail(f"Input {file_type} file not found: {file_path}")
    
    async def send_api_request(self, file_paths: Dict[str, str], pdf_filename: str, 
                              license_data: Dict[str, Any]) -> httpx.Response:
        """
        Send API request to process document.
        
        Args:
            file_paths: Dictionary containing file paths
            pdf_filename: Name of the PDF file
            license_data: License data containing token and user ID
            
        Returns:
            httpx.Response: API response
        """
        # Prepare headers
        headers = {
            "Authorization": f"Bearer {license_data['Token']}", 
            "iUserid": str(license_data.get('uid', 'unknown'))
        }
        
        # Calculate checksum
        checksum = calculate_checksum(file_paths['input_pdf'], algorithm="md5")
        
        # Prepare metadata
        lsClientDocMetaData = [{
            "filename": pdf_filename,
            "Type": ".pdf",
            "location": file_paths['input_pdf'],
            "checksum": checksum
        }]
        
        # Prepare parameters
        params = {
            "bTestMode": TestConfig.API_CONFIG["bTestMode"],
            "strVoucherType": TestConfig.API_CONFIG["strVoucherType"],
            "lsClientDocMetaData": json.dumps(lsClientDocMetaData),
            "bIsMultivendorDoc": TestConfig.API_CONFIG["bIsMultivendorDoc"],
            "strSystemName": os.getlogin(),
            "strSerializeUserConfig": json.dumps(TestConfig.API_CONFIG["strSerializeUserConfig"])
        }
        
        data = {"checksums": checksum}
        
        # Send request
        self.logger.MSWriteLog("info", f"Sending POST request to {TestConfig.API_URL}")
        try:
            with httpx.Client(timeout=TestConfig.API_TIMEOUT) as client:
                response = client.post(
                    TestConfig.API_URL,
                    headers=headers,
                    params=params,
                    files=[("documents", (pdf_filename, open(file_paths['input_pdf'], "rb"), "application/pdf"))],
                    data=data
                )
            return response
        except Exception as e:
            self.logger.MSWriteLog("error", f"API request failed: {str(e)}")
            self.fail(f"API request failed: {str(e)}")
    
    def validate_and_save_response(self, response: httpx.Response, zip_path: str):
        """
        Validate API response and save ZIP file.
        
        Args:
            response: API response
            zip_path: Path to save ZIP file
        """
        # Validate response status
        if response.status_code != 200:
            self.logger.MSWriteLog("error", f"Non-200 response: {response.status_code}, content: {response.text}")
            self.fail(f"Expected status code 200, got {response.status_code}")
        
        # Validate content type
        self.assertEqual(
            response.headers["Content-Type"], 
            "application/zip", 
            f"Expected Content-Type application/zip, got {response.headers['Content-Type']}"
        )
        
        # Verify checksum
        response_checksum = response.headers.get("X-Zip-Checksum")
        if not response_checksum:
            self.logger.MSWriteLog("error", "Checksum not received in response headers")
            self.fail("Checksum not received in response headers")
        
        # Save ZIP file
        with open(zip_path, "wb") as f:
            for chunk in response.iter_bytes():
                f.write(chunk)
        
        # Verify saved file checksum
        saved_checksum = calculate_checksum(zip_path, algorithm="md5")
        self.assertEqual(saved_checksum, response_checksum, "Checksum mismatch! The downloaded file is corrupted")
        
        self.logger.MSWriteLog("info", f"ZIP file saved to: {zip_path}")
    
    def extract_and_rename_files(self, zip_path: str, output_dir: str, doc_name: str,
                                xml_pattern: str, csv_pattern: str) -> Tuple[str, str]:
        """
        Extract and rename XML and CSV files from ZIP.
        
        Args:
            zip_path: Path to ZIP file
            output_dir: Output directory
            doc_name: Document name for renaming
            xml_pattern: Pattern to find XML file in ZIP
            csv_pattern: Pattern to find CSV file in ZIP
            
        Returns:
            Tuple of (xml_path, csv_path)
        """
        # Extract files
        xml_path, csv_path = extract_zip(zip_path, output_dir, xml_pattern, csv_pattern)
        
        # Verify extraction
        self.assertTrue(os.path.exists(xml_path), f"XML file not found at: {xml_path}")
        self.logger.MSWriteLog("info", f"XML extracted to: {xml_path}")
        
        self.assertTrue(os.path.exists(csv_path), f"CSV file not found at: {csv_path}")
        self.logger.MSWriteLog("info", f"CSV extracted to: {csv_path}")
        
        # Rename to standard names
        standard_xml_path = os.path.join(output_dir, f"{doc_name}.xml")
        standard_csv_path = os.path.join(output_dir, f"Report_{doc_name}.csv")
        
        if os.path.basename(xml_path) != f"{doc_name}.xml":
            os.rename(xml_path, standard_xml_path)
            xml_path = standard_xml_path
            self.logger.MSWriteLog("info", f"Renamed XML to: {xml_path}")
        
        if os.path.basename(csv_path) != f"Report_{doc_name}.csv":
            os.rename(csv_path, standard_csv_path)
            csv_path = standard_csv_path
            self.logger.MSWriteLog("info", f"Renamed CSV to: {csv_path}")
        
        return xml_path, csv_path
    
    def compare_files(self, file_paths: Dict[str, str], extracted_xml_path: str, extracted_csv_path: str):
        """
        Compare extracted files with reference files.
        
        Args:
            file_paths: Dictionary containing file paths
            extracted_xml_path: Path to extracted XML file
            extracted_csv_path: Path to extracted CSV file
        """
        if TestConfig.BRELEARN:
            self.logger.MSWriteLog("info", "BRELEARN mode: Skipping file comparison")
            return
        
        # Compare XML files
        self.logger.MSWriteLog("info", f"Comparing XML: {file_paths['input_xml']} vs {extracted_xml_path}")
        bAreSameXml, strLogMessageXml = compare_xml(
            self.logger, 
            file_paths['input_xml'], 
            extracted_xml_path, 
            ignore_tags=TestConfig.XML_IGNORE_TAGS
        )
        if not bAreSameXml:
            self.logger.MSWriteLog("error", f"XML content mismatch: {strLogMessageXml}")
            self.fail(f"XML content mismatch: {strLogMessageXml}")
        self.logger.MSWriteLog("info", "XML content matched")
        
        # Compare CSV files
        self.logger.MSWriteLog("info", f"Comparing CSV: {file_paths['input_csv']} vs {extracted_csv_path}")
        bAreSameCsv, strLogMessageCsv = compare_csv(
            self.logger, 
            file_paths['input_csv'], 
            extracted_csv_path, 
            ignore_columns=TestConfig.CSV_IGNORE_COLUMNS
        )
        if not bAreSameCsv:
            self.logger.MSWriteLog("error", f"CSV content mismatch: {strLogMessageCsv}")
            self.fail(f"CSV content mismatch: {strLogMessageCsv}")
        self.logger.MSWriteLog("info", "CSV content matched")
